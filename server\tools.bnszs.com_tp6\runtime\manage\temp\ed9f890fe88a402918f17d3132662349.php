<?php /*a:2:{s:102:"E:\Build\C++\bnspatch\src\BnsPlugin\server\tools.bnszs.com_tp6\app\manage\view\announcement\index.html";i:1752168875;s:99:"E:\Build\C++\bnspatch\src\BnsPlugin\server\tools.bnszs.com_tp6\app\manage\view\manage\template.html";i:1752168837;}*/ ?>
<!DOCTYPE html>
<html>
<head>
  <!-- 项目构建：兔子、0x1ng、Xylia  | 项目创建:2020-06-01  | 项目更新:2025-02-05 -->
  <meta charset="utf-8">
  <meta http-equiv="X-UA-Compatible" content="IE=edge">
  <meta name="description" content="剑灵小助手管理系统。">
  <meta name="keywords" content="剑灵骗子,剑灵骗子大全,游戏骗子,剑灵骗子数据库,小助手骗子数据库,小助手提交骗子,小助手自定义,装备查询优化,装备查询自定义,小助手装备查询,剑灵装备查询,剑灵小助手">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <meta name="renderer" content="webkit">
  <meta name="apple-mobile-web-app-title" content="Amaze UI" />
  <meta http-equiv="Cache-Control" content="no-siteapp" />

  <title>剑灵小助手管理系统</title>
  <link rel="icon shortcut" href="/favicon.ico" type="image/x-icon">
  <link rel="stylesheet" href="https://lf6-cdn-tos.bytecdntp.com/cdn/expire-1-M/bootstrap/5.1.3/css/bootstrap.css">
  <link rel="stylesheet" href="/css/manage.css?version=2025021504"/>
  <link rel="stylesheet" href="/css/admin.css?version=2021021110">
  <link rel="stylesheet" href="/css/tally.css?version=2021021112">

  <script src="https://lf3-cdn-tos.bytecdntp.com/cdn/expire-1-M/jquery/3.6.0/jquery.min.js"></script>
  <script src="https://lf6-cdn-tos.bytecdntp.com/cdn/expire-1-M/bootstrap/5.1.3/js/bootstrap.min.js"></script>
  <script src="https://static-1251192097.cos.ap-shanghai.myqcloud.com/web_html/assets/layer/layer.js"></script>
  <script src="/js/amazeui.min.js"></script>
  <script src="/js/manage.js"></script>

  <script>
  // 移动设备侧边栏控制函数 - 全局定义
  function toggleMobileSidebar() {
    console.log('toggleMobileSidebar called');
    var sidebar = document.getElementById('admin-offcanvas');
    var overlay = document.querySelector('.mobile-sidebar-overlay');

    console.log('sidebar:', sidebar);
    console.log('overlay:', overlay);

    if (!sidebar) {
      console.error('Sidebar not found!');
      return;
    }

    if (sidebar.classList.contains('mobile-show')) {
      console.log('Hiding sidebar');
      hideMobileSidebar();
    } else {
      console.log('Showing sidebar');
      showMobileSidebar();
    }
  }

  function showMobileSidebar() {
    console.log('showMobileSidebar called');
    var sidebar = document.getElementById('admin-offcanvas');
    var overlay = document.querySelector('.mobile-sidebar-overlay');

    if (sidebar) {
      sidebar.classList.add('mobile-show');
      console.log('Added mobile-show class to sidebar');
    }

    if (overlay) {
      overlay.classList.add('show');
      console.log('Added show class to overlay');
    }
  }

  function hideMobileSidebar() {
    console.log('hideMobileSidebar called');
    var sidebar = document.getElementById('admin-offcanvas');
    var overlay = document.querySelector('.mobile-sidebar-overlay');

    if (sidebar) {
      sidebar.classList.remove('mobile-show');
      console.log('Removed mobile-show class from sidebar');
    }

    if (overlay) {
      overlay.classList.remove('show');
      console.log('Removed show class from overlay');
    }
  }
  </script>

  <style type="text/css">
  	.ripple {
		position: relative;
		overflow: hidden;
	}
			
	.ripple:after {
		content: "";
		display: block;
		position: absolute;
		width: 100%;
		height: 100%;
		top: 0;
		left: 0;
		pointer-events: none;
		background-image: radial-gradient(circle, #666 10%, transparent 10.01%);
		background-repeat: no-repeat;
		background-position: 50%;
		transform: scale(10, 10);
		opacity: 0;
		transition: transform .3s, opacity .5s;
	}
			
	.ripple:active:after {
		transform: scale(0, 0);
		opacity: .3;
		transition: 0s;
	}

	.btn-sign{
		border-width:0px;
		width: 80px;
		height: 80px;
	}
			
	.option {
		width: 200px;
		height: 40px;
		border: 1px solid #cccccc;
		position: relative;
	}

	.option select {
		border: none;
		outline: none;
		width: 100%;
		height: 40px;
		line-height: 40px;
		appearance: none;
		-webkit-appearance: none;
		-moz-appearance: none;
		padding-left: 20px;
	}

	.option:after {
		content: "";
		width: 14px;
		height: 8px;
		background: url(/assets/arrow-down.png) no-repeat center;
		position: absolute;
		right: 20px;
		top: 41%;
		pointer-events: none;
	}

	/* 统计卡片样式 */
	.stats-card {
		background: #f8f9fa;
		border: 1px solid #e9ecef;
		border-radius: 8px;
		padding: 20px;
		text-align: center;
		margin-bottom: 15px;
		transition: all 0.3s ease;
	}

	.stats-card:hover {
		background: #e9ecef;
		transform: translateY(-2px);
		box-shadow: 0 4px 8px rgba(0,0,0,0.1);
	}

	.stats-number {
		font-size: 2.5em;
		font-weight: bold;
		color: #007bff;
		margin-bottom: 5px;
	}

	.stats-label {
		color: #6c757d;
		font-size: 0.9em;
		text-transform: uppercase;
		letter-spacing: 0.5px;
	}

	/* 统计卡片链接样式 */
	.stats-card-link {
		display: block;
		text-decoration: none;
		color: inherit;
	}

	.stats-card-link:hover {
		text-decoration: none;
		color: inherit;
	}

	/* 状态颜色 */
	.status-normal { color: #28a745; }
	.status-banned { color: #dc3545; }
	.status-premium { color: #ffc107; }
	.status-online { color: #17a2b8; }

	/* 移动设备侧边栏优化 */
	@media only screen and (max-width: 640px) {
		#admin-offcanvas {
			position: fixed !important;
			left: -260px !important;
			top: 51px !important;
			bottom: 0 !important;
			z-index: 1600 !important;
			transition: left 0.3s ease !important;
			background: #fff !important;
			box-shadow: 2px 0 5px rgba(0,0,0,0.1) !important;
			width: 260px !important;
			height: calc(100vh - 51px) !important;
			overflow-y: auto !important;
			border: 1px solid #ddd !important;
		}

		#admin-offcanvas.mobile-show {
			left: 0 !important;
		}

		/* 确保侧边栏内容可见 */
		#admin-offcanvas .am-offcanvas-bar {
			position: static !important;
			transform: none !important;
			width: 100% !important;
			height: 100% !important;
			background: #fff !important;
			padding: 10px !important;
		}

		/* 确保侧边栏菜单项可见 */
		#admin-offcanvas .admin-sidebar-list {
			background: #fff !important;
			margin: 0 !important;
			padding: 0 !important;
		}

		#admin-offcanvas .admin-sidebar-list li {
			background: #fff !important;
			border-bottom: 1px solid #eee !important;
		}

		#admin-offcanvas .admin-sidebar-list li a {
			color: #333 !important;
			padding: 12px 15px !important;
			display: block !important;
			text-decoration: none !important;
		}

		#admin-offcanvas .admin-sidebar-list li a:hover {
			background: #f5f5f5 !important;
			color: #1E9FFF !important;
		}

		.admin-content {
			margin-left: 0 !important;
		}

		.mobile-sidebar-toggle {
			background: #1E9FFF !important;
			border-color: #1E9FFF !important;
			color: white !important;
			border: none !important;
			padding: 6px 12px !important;
			border-radius: 3px !important;
		}

		.mobile-sidebar-toggle:hover {
			background: #0e7ce8 !important;
			border-color: #0e7ce8 !important;
		}

		/* 确保移动端菜单按钮在右侧正确显示 */
		.am-topbar-right .am-show-sm-only {
			float: right;
		}

		.am-topbar-right .mobile-sidebar-toggle {
			margin: 8px 10px 8px 0;
		}

		/* 遮罩层 */
		.mobile-sidebar-overlay {
			position: fixed;
			top: 51px;
			left: 0;
			right: 0;
			bottom: 0;
			background: rgba(0,0,0,0.5);
			z-index: 1500;
			display: none;
		}

		.mobile-sidebar-overlay.show {
			display: block;
		}
	}
  </style>
</head>
<body>
	<header class="am-topbar am-topbar-inverse admin-header">
	  <div class="am-topbar-brand">
		<strong>剑灵小助手管理系统</strong> <small> 堕络</small>
	  </div>

	  <div class="am-collapse am-topbar-collapse" id="topbar-collapse">
		<ul class="am-nav am-nav-pills am-topbar-nav am-topbar-right admin-header-list">
			<?php if(session("admin")): ?>
			<li class="am-dropdown" data-am-dropdown>
				<a class="am-dropdown-toggle" data-am-dropdown-toggle href="javascript:void(0);" onclick="SwitchPanel('#admin-dropdown-content')">
				  <span class="am-icon-admin"></span> 管理员 <span class="am-icon-caret-down"></span>
				</a>
				<ul class="am-dropdown-content" id="admin-dropdown-content">
				  <li><a href="javascript:void(0);" onclick="window.location.href='/admin/userinfo.php?id=1'"><span class="am-icon-admin"></span> 资料</a></li>
				  <li><a href="javascript:void(0);" onclick="logout()"><span class="am-icon-power-off"></span> 退出</a></li>
				</ul>
			  </li>
			<?php endif; ?>
			<li class="am-hide-sm-only">
				<?php if(session("user")): ?>
				<a href="/manage/center" id="user">
					<span class="am-icon-users"></span>
					<span class="admin-fullText">个人中心</span>
				</a>
				<?php else: ?>
				<a href="/manage/login" id="user">
					<span class="am-icon-users"></span>
					<span class="admin-fullText">登录账号</span>
				</a>
				<?php endif; ?>
			</li>
			<!-- 移动设备侧边栏切换按钮 - 放在最右边 -->
			<li class="am-show-sm-only">
				<button class="am-topbar-btn am-btn am-btn-sm am-btn-primary mobile-sidebar-toggle" onclick="toggleMobileSidebar()">
					<span class="am-sr-only">菜单</span> <span class="am-icon-navicon"></span>
				</button>
			</li>
		</ul>
	  </div>
	</header>

	<div class="am-cf admin-main">
	  <!-- sidebar start -->
	  <div class="admin-sidebar am-offcanvas" id="admin-offcanvas">
		<div class="am-offcanvas-bar admin-offcanvas-bar">
		  <ul class="am-list admin-sidebar-list">
			<li><a href="/manage"><span class="am-icon-home"></span> 系统首页</a></li>
			<!-- <li><a href="/manage/liars"><span class="am-icon-th"></span> 骗子列表 <span class="am-badge am-badge-secondary am-margin-right am-fr"></span></a></li> -->
			<!-- <li>
			  <a href="/manage/liarpost"><span class="am-icon-pencil-square-o"></span> <?php if(session('admin')) echo('提交骗子<span class="am-badge am-badge-secondary am-margin-right am-fr">管理</span>'); 		
				  else echo('举报骗子'); 
			  ?></a>
			</li> -->
			<?php if(isset($_SESSION['admin']) && $_SESSION['admin']): 			try {
				$adminMenuItems = app\manage\model\UserAdmin::GetItems();
				if (!empty($adminMenuItems)) {
					foreach ($adminMenuItems as $item) {?>
						<li><a href="<?php echo $item['url']; ?>"><span class="<?php echo $item['icon']; ?>"></span> <?php echo $item['itemName']; ?></a></li>
					<?php }
				} else { ?>
					<li><a href="#"><span class="am-icon-warning"></span> 暂无管理菜单</a></li>
				<?php }
			} catch (Exception $e) { ?>
				<li><a href="#"><span class="am-icon-exclamation-triangle"></span> 菜单加载失败</a></li>
			<?php } ?>
			<?php endif; ?>
			<li><a href="/manage/profile"><span class="am-icon-gift"></span> 自定义资料</a></li>
			<?php if(session('user')){ ?> <li><a href="/manage/logout"><span class="am-icon-sign-out"></span> 注销</a></li> <?php } ?>
		  </ul>
		  
		</div>
	  </div>

	  <!-- 移动设备侧边栏遮罩层 -->
	  <div class="mobile-sidebar-overlay" onclick="hideMobileSidebar()"></div>

	  

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">公告管理</h5>
                    <a href="/manage/admin/announcement/add" class="btn btn-primary btn-sm">
                        <i class="fas fa-plus"></i> 添加公告
                    </a>
                </div>
                
                <div class="card-body">
                    <!-- 统计信息 -->
                    <div class="row mb-3">
                        <div class="col-md-3">
                            <div class="card bg-primary text-white">
                                <div class="card-body">
                                    <h6>总公告数</h6>
                                    <h4><?php echo htmlentities((string) $stats['total']); ?></h4>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card bg-success text-white">
                                <div class="card-body">
                                    <h6>已发布</h6>
                                    <h4><?php echo htmlentities((string) $stats['published']); ?></h4>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card bg-warning text-white">
                                <div class="card-body">
                                    <h6>草稿</h6>
                                    <h4><?php echo htmlentities((string) $stats['draft']); ?></h4>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card bg-info text-white">
                                <div class="card-body">
                                    <h6>今日新增</h6>
                                    <h4><?php echo htmlentities((string) $stats['today']); ?></h4>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 搜索过滤 -->
                    <form method="get" class="mb-3">
                        <div class="row">
                            <div class="col-md-3">
                                <select name="status" class="form-select">
                                    <option value="">全部状态</option>
                                    <option value="0" <?php if($filters['status'] == '0'): ?>selected<?php endif; ?>>草稿</option>
                                    <option value="1" <?php if($filters['status'] == '1'): ?>selected<?php endif; ?>>已发布</option>
                                    <option value="2" <?php if($filters['status'] == '2'): ?>selected<?php endif; ?>>已下线</option>
                                </select>
                            </div>
                            <div class="col-md-3">
                                <select name="type" class="form-select">
                                    <option value="">全部类型</option>
                                    <option value="1" <?php if($filters['type'] == '1'): ?>selected<?php endif; ?>>普通公告</option>
                                    <option value="2" <?php if($filters['type'] == '2'): ?>selected<?php endif; ?>>重要公告</option>
                                    <option value="3" <?php if($filters['type'] == '3'): ?>selected<?php endif; ?>>紧急公告</option>
                                </select>
                            </div>
                            <div class="col-md-4">
                                <input type="text" name="keyword" class="form-control" placeholder="搜索标题或内容" value="<?php echo htmlentities((string) $filters['keyword']); ?>">
                            </div>
                            <div class="col-md-2">
                                <button type="submit" class="btn btn-outline-primary">搜索</button>
                            </div>
                        </div>
                    </form>
                    
                    <!-- 批量操作 -->
                    <div class="mb-3">
                        <button type="button" class="btn btn-sm btn-success" onclick="batchAction('publish')">批量发布</button>
                        <button type="button" class="btn btn-sm btn-warning" onclick="batchAction('offline')">批量下线</button>
                        <button type="button" class="btn btn-sm btn-danger" onclick="batchAction('delete')">批量删除</button>
                    </div>
                    
                    <!-- 公告列表 -->
                    <div class="table-responsive">
                        <table class="table table-striped">
                            <thead>
                                <tr>
                                    <th><input type="checkbox" id="checkAll"></th>
                                    <th>ID</th>
                                    <th>标题</th>
                                    <th>类型</th>
                                    <th>状态</th>
                                    <th>优先级</th>
                                    <th>目标客户端</th>
                                    <th>查看次数</th>
                                    <th>发布者</th>
                                    <th>创建时间</th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php if(is_array($announcements) || $announcements instanceof \think\Collection || $announcements instanceof \think\Paginator): $i = 0; $__LIST__ = $announcements;if( count($__LIST__)==0 ) : echo "" ;else: foreach($__LIST__ as $key=>$item): $mod = ($i % 2 );++$i;?>
                                <tr>
                                    <td><input type="checkbox" class="item-check" value="<?php echo htmlentities((string) $item['id']); ?>"></td>
                                    <td><?php echo htmlentities((string) $item['id']); ?></td>
                                    <td>
                                        <div class="text-truncate" style="max-width: 200px;" title="<?php echo htmlentities((string) $item['title']); ?>">
                                            <?php echo htmlentities((string) $item['title']); ?>
                                        </div>
                                    </td>
                                    <td>
                                        <?php if($item['type'] == 1): ?>
                                            <span class="badge bg-secondary">普通</span>
                                        <?php elseif($item['type'] == 2): ?>
                                            <span class="badge bg-warning">重要</span>
                                        <?php elseif($item['type'] == 3): ?>
                                            <span class="badge bg-danger">紧急</span>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <?php if($item['status'] == 0): ?>
                                            <span class="badge bg-secondary">草稿</span>
                                        <?php elseif($item['status'] == 1): ?>
                                            <span class="badge bg-success">已发布</span>
                                        <?php elseif($item['status'] == 2): ?>
                                            <span class="badge bg-dark">已下线</span>
                                        <?php endif; ?>
                                    </td>
                                    <td><?php echo htmlentities((string) $item['priority']); ?></td>
                                    <td>
                                        <?php if($item['target_client'] == 'all'): ?>全部
                                        <?php elseif($item['target_client'] == 'desktop'): ?>桌面端
                                        <?php elseif($item['target_client'] == 'mobile'): ?>移动端
                                        <?php endif; ?>
                                    </td>
                                    <td><?php echo htmlentities((string) $item['view_count']); ?></td>
                                    <td><?php echo htmlentities((string) $item['admin_username']); ?></td>
                                    <td><?php echo htmlentities((string) date('Y-m-d H:i',!is_numeric($item['created_at'])? strtotime($item['created_at']) : $item['created_at'])); ?></td>
                                    <td>
                                        <div class="btn-group btn-group-sm">
                                            <a href="/manage/admin/announcement/edit/<?php echo htmlentities((string) $item['id']); ?>" class="btn btn-outline-primary btn-sm">编辑</a>
                                            <?php if($item['status'] == 0): ?>
                                                <button type="button" class="btn btn-outline-success btn-sm" onclick="publishAnnouncement(<?php echo htmlentities((string) $item['id']); ?>)">发布</button>
                                            <?php elseif($item['status'] == 1): ?>
                                                <button type="button" class="btn btn-outline-warning btn-sm" onclick="offlineAnnouncement(<?php echo htmlentities((string) $item['id']); ?>)">下线</button>
                                            <?php endif; ?>
                                            <button type="button" class="btn btn-outline-danger btn-sm" onclick="deleteAnnouncement(<?php echo htmlentities((string) $item['id']); ?>)">删除</button>
                                        </div>
                                    </td>
                                </tr>
                                <?php endforeach; endif; else: echo "" ;endif; ?>
                            </tbody>
                        </table>
                    </div>
                    
                    <!-- 分页 -->
                    <div class="d-flex justify-content-center">
                        <?php echo $announcements->appends(request()->param())->render(); ?>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// 全选/取消全选
document.getElementById('checkAll').addEventListener('change', function() {
    const checkboxes = document.querySelectorAll('.item-check');
    checkboxes.forEach(checkbox => {
        checkbox.checked = this.checked;
    });
});

// 批量操作
function batchAction(action) {
    const checkedItems = document.querySelectorAll('.item-check:checked');
    if (checkedItems.length === 0) {
        layer.msg('请选择要操作的公告');
        return;
    }
    
    const ids = Array.from(checkedItems).map(item => item.value);
    const actionText = action === 'publish' ? '发布' : (action === 'offline' ? '下线' : '删除');
    
    layer.confirm(`确定要${actionText}选中的 ${ids.length} 条公告吗？`, function(index) {
        $.post('/manage/admin/announcement', {
            action: 'batch',
            batch_action: action,
            ids: ids
        }, function(res) {
            if (res.code === 1) {
                layer.msg(res.msg, {icon: 1});
                setTimeout(() => location.reload(), 1000);
            } else {
                layer.msg(res.msg, {icon: 2});
            }
        });
        layer.close(index);
    });
}

// 发布公告
function publishAnnouncement(id) {
    layer.confirm('确定要发布这条公告吗？', function(index) {
        $.post('/manage/admin/announcement', {
            action: 'publish',
            id: id
        }, function(res) {
            if (res.code === 1) {
                layer.msg(res.msg, {icon: 1});
                setTimeout(() => location.reload(), 1000);
            } else {
                layer.msg(res.msg, {icon: 2});
            }
        });
        layer.close(index);
    });
}

// 下线公告
function offlineAnnouncement(id) {
    layer.confirm('确定要下线这条公告吗？', function(index) {
        $.post('/manage/admin/announcement', {
            action: 'offline',
            id: id
        }, function(res) {
            if (res.code === 1) {
                layer.msg(res.msg, {icon: 1});
                setTimeout(() => location.reload(), 1000);
            } else {
                layer.msg(res.msg, {icon: 2});
            }
        });
        layer.close(index);
    });
}

// 删除公告
function deleteAnnouncement(id) {
    layer.confirm('确定要删除这条公告吗？删除后无法恢复！', function(index) {
        $.post('/manage/admin/announcement', {
            action: 'delete',
            id: id
        }, function(res) {
            if (res.code === 1) {
                layer.msg(res.msg, {icon: 1});
                setTimeout(() => location.reload(), 1000);
            } else {
                layer.msg(res.msg, {icon: 2});
            }
        });
        layer.close(index);
    });
}
</script>

	   
	  <div id="outerdiv" style="position:fixed;top:0;left:0;background:rgba(0,0,0,0.7);z-index:2;width:100%;height:100%;display:none;">
		<div id="innerdiv" style="position:absolute;">
		  <img id="bigimg" style="border:5px solid #fff;" src="" />
		</div>
	  </div>
	  <footer class="admin-content-footer">
		<hr>
		<p class="am-padding-left">© 2018 duoluosb.</p>
	  </footer>
	</div>

	<script>
	// 页面初始化脚本

	// 窗口大小改变时隐藏移动侧边栏
	window.addEventListener('resize', function() {
		if (window.innerWidth > 640) {
			hideMobileSidebar();
		}
	});

	// 页面加载完成后的初始化
	document.addEventListener('DOMContentLoaded', function() {
		// 添加触摸事件支持
		var toggleBtn = document.querySelector('.mobile-sidebar-toggle');
		if (toggleBtn) {
			toggleBtn.addEventListener('touchstart', function(e) {
				e.preventDefault();
				toggleMobileSidebar();
			});
		}
	});
	</script>
</body>

<script>
    var _hmt = _hmt || [];
    (function() {
      var hm = document.createElement("script");
      hm.src = "https://hm.baidu.com/hm.js?00e000ae4edf31394d2153c309efbdec";
      var s = document.getElementsByTagName("script")[0]; 
      s.parentNode.insertBefore(hm, s);
    })();
</script>