{layout name="manage/template" /}

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">公告管理</h5>
                    <a href="/manage/admin/announcement/add" class="btn btn-primary btn-sm">
                        <i class="fas fa-plus"></i> 添加公告
                    </a>
                </div>
                
                <div class="card-body">
                    <!-- 统计信息 -->
                    <div class="row mb-3">
                        <div class="col-md-3">
                            <div class="card bg-primary text-white">
                                <div class="card-body">
                                    <h6>总公告数</h6>
                                    <h4>{$stats.total}</h4>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card bg-success text-white">
                                <div class="card-body">
                                    <h6>已发布</h6>
                                    <h4>{$stats.published}</h4>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card bg-warning text-white">
                                <div class="card-body">
                                    <h6>草稿</h6>
                                    <h4>{$stats.draft}</h4>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card bg-info text-white">
                                <div class="card-body">
                                    <h6>今日新增</h6>
                                    <h4>{$stats.today}</h4>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 搜索过滤 -->
                    <form method="get" class="mb-3">
                        <div class="row">
                            <div class="col-md-3">
                                <select name="status" class="form-select">
                                    <option value="">全部状态</option>
                                    <option value="0" {if $filters.status == '0'}selected{/if}>草稿</option>
                                    <option value="1" {if $filters.status == '1'}selected{/if}>已发布</option>
                                    <option value="2" {if $filters.status == '2'}selected{/if}>已下线</option>
                                </select>
                            </div>
                            <div class="col-md-3">
                                <select name="type" class="form-select">
                                    <option value="">全部类型</option>
                                    <option value="1" {if $filters.type == '1'}selected{/if}>普通公告</option>
                                    <option value="2" {if $filters.type == '2'}selected{/if}>重要公告</option>
                                    <option value="3" {if $filters.type == '3'}selected{/if}>紧急公告</option>
                                </select>
                            </div>
                            <div class="col-md-4">
                                <input type="text" name="keyword" class="form-control" placeholder="搜索标题或内容" value="{$filters.keyword}">
                            </div>
                            <div class="col-md-2">
                                <button type="submit" class="btn btn-outline-primary">搜索</button>
                            </div>
                        </div>
                    </form>
                    
                    <!-- 批量操作 -->
                    <div class="mb-3">
                        <button type="button" class="btn btn-sm btn-success" onclick="batchAction('publish')">批量发布</button>
                        <button type="button" class="btn btn-sm btn-warning" onclick="batchAction('offline')">批量下线</button>
                        <button type="button" class="btn btn-sm btn-danger" onclick="batchAction('delete')">批量删除</button>
                    </div>
                    
                    <!-- 公告列表 -->
                    <div class="table-responsive">
                        <table class="table table-striped">
                            <thead>
                                <tr>
                                    <th><input type="checkbox" id="checkAll"></th>
                                    <th>ID</th>
                                    <th>标题</th>
                                    <th>类型</th>
                                    <th>状态</th>
                                    <th>优先级</th>
                                    <th>目标客户端</th>
                                    <th>查看次数</th>
                                    <th>发布者</th>
                                    <th>创建时间</th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody>
                                {volist name="announcements" id="item"}
                                <tr>
                                    <td><input type="checkbox" class="item-check" value="{$item.id}"></td>
                                    <td>{$item.id}</td>
                                    <td>
                                        <div class="text-truncate" style="max-width: 200px;" title="{$item.title}">
                                            {$item.title}
                                        </div>
                                    </td>
                                    <td>
                                        {if $item.type == 1}
                                            <span class="badge bg-secondary">普通</span>
                                        {elseif $item.type == 2}
                                            <span class="badge bg-warning">重要</span>
                                        {elseif $item.type == 3}
                                            <span class="badge bg-danger">紧急</span>
                                        {/if}
                                    </td>
                                    <td>
                                        {if $item.status == 0}
                                            <span class="badge bg-secondary">草稿</span>
                                        {elseif $item.status == 1}
                                            <span class="badge bg-success">已发布</span>
                                        {elseif $item.status == 2}
                                            <span class="badge bg-dark">已下线</span>
                                        {/if}
                                    </td>
                                    <td>{$item.priority}</td>
                                    <td>
                                        {if $item.target_client == 'all'}全部
                                        {elseif $item.target_client == 'desktop'}桌面端
                                        {elseif $item.target_client == 'mobile'}移动端
                                        {/if}
                                    </td>
                                    <td>{$item.view_count}</td>
                                    <td>{$item.admin_username}</td>
                                    <td>{$item.created_at|date='Y-m-d H:i'}</td>
                                    <td>
                                        <div class="btn-group btn-group-sm">
                                            <a href="/manage/admin/announcement/edit/{$item.id}" class="btn btn-outline-primary btn-sm">编辑</a>
                                            {if $item.status == 0}
                                                <button type="button" class="btn btn-outline-success btn-sm" onclick="publishAnnouncement({$item.id})">发布</button>
                                            {elseif $item.status == 1}
                                                <button type="button" class="btn btn-outline-warning btn-sm" onclick="offlineAnnouncement({$item.id})">下线</button>
                                            {/if}
                                            <button type="button" class="btn btn-outline-danger btn-sm" onclick="deleteAnnouncement({$item.id})">删除</button>
                                        </div>
                                    </td>
                                </tr>
                                {/volist}
                            </tbody>
                        </table>
                    </div>
                    
                    <!-- 分页 -->
                    <div class="d-flex justify-content-center">
                        {$announcements->appends(request()->param())->render()|raw}
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// 全选/取消全选
document.getElementById('checkAll').addEventListener('change', function() {
    const checkboxes = document.querySelectorAll('.item-check');
    checkboxes.forEach(checkbox => {
        checkbox.checked = this.checked;
    });
});

// 批量操作
function batchAction(action) {
    const checkedItems = document.querySelectorAll('.item-check:checked');
    if (checkedItems.length === 0) {
        layer.msg('请选择要操作的公告');
        return;
    }
    
    const ids = Array.from(checkedItems).map(item => item.value);
    const actionText = action === 'publish' ? '发布' : (action === 'offline' ? '下线' : '删除');
    
    layer.confirm(`确定要${actionText}选中的 ${ids.length} 条公告吗？`, function(index) {
        $.post('/manage/admin/announcement/batch', {
            action: action,
            ids: ids
        }, function(res) {
            if (res.code === 1) {
                layer.msg(res.msg, {icon: 1});
                setTimeout(() => location.reload(), 1000);
            } else {
                layer.msg(res.msg, {icon: 2});
            }
        });
        layer.close(index);
    });
}

// 发布公告
function publishAnnouncement(id) {
    layer.confirm('确定要发布这条公告吗？', function(index) {
        $.post('/manage/admin/announcement/publish/' + id, function(res) {
            if (res.code === 1) {
                layer.msg(res.msg, {icon: 1});
                setTimeout(() => location.reload(), 1000);
            } else {
                layer.msg(res.msg, {icon: 2});
            }
        });
        layer.close(index);
    });
}

// 下线公告
function offlineAnnouncement(id) {
    layer.confirm('确定要下线这条公告吗？', function(index) {
        $.post('/manage/admin/announcement/offline/' + id, function(res) {
            if (res.code === 1) {
                layer.msg(res.msg, {icon: 1});
                setTimeout(() => location.reload(), 1000);
            } else {
                layer.msg(res.msg, {icon: 2});
            }
        });
        layer.close(index);
    });
}

// 删除公告
function deleteAnnouncement(id) {
    layer.confirm('确定要删除这条公告吗？删除后无法恢复！', function(index) {
        $.post('/manage/admin/announcement/delete/' + id, function(res) {
            if (res.code === 1) {
                layer.msg(res.msg, {icon: 1});
                setTimeout(() => location.reload(), 1000);
            } else {
                layer.msg(res.msg, {icon: 2});
            }
        });
        layer.close(index);
    });
}
</script>
