﻿using System.ComponentModel;
using System.Diagnostics;
using System.Runtime.CompilerServices;
using System.Windows.Media.Imaging;
using Xylia.BnsHelper.Services.Network.Service;
using Xylia.BnsHelper.Views;

namespace Xylia.BnsHelper.Models;
internal class User : IDisposable, INotifyPropertyChanged
{
    #region Constructor
    public User(BnszsSession session, long uin)
    {
        SetSession(session);
        Uin = uin;
    }
    #endregion

    #region Fields
    public long Uin { get; init; }

    private byte _permission = 0;

    /// <summary>
    /// 用户当前权限级别
    /// </summary>
    public byte Permission
    {
        get
        {
            // 这是为了 DamageMeterViewModel 能正确判断是否已经过期，不存在逻辑问题
            if (PermissionExpiration < DateTime.Now) return _permission = 0;
            return _permission;
        }
        set
        {
            if (_permission != value)
            {
                _permission = value;
                OnPropertyChanged();
                OnPropertyChanged(nameof(HeadImg)); // 通知头像属性更新
                OnPropertyChanged(nameof(PermissionText)); // 通知权限文本更新
                OnPropertyChanged(nameof(ShowActivityNotice)); // 通知活动提示显示状态更新
            }

            // 时间每次都要刷新
            OnPropertyChanged(nameof(PermissionExpiration));
            OnPropertyChanged(nameof(ExpirationText)); 
        }
    }

    /// <summary>
    /// 用户权限过期时间
    /// </summary>
    public DateTime? PermissionExpiration { get; private set; }

    /// <summary>
    /// 权限级别文本
    /// </summary>
    public string PermissionText => Permission switch
    {
        0 => "普通用户",
        1 => "高级用户",
        2 => "会员用户",
        _ => "未知权限"
    };

    /// <summary>
    /// 权限过期时间文本
    /// </summary>
    public string? ExpirationText
    {
        get
        {
            // 首先检查权限过期时间（服务端发送的时间戳）
            if (PermissionExpiration == null) return null;
            else if (PermissionExpiration == DateTime.MaxValue) return "永久权限";

            // 检查是否在活动期间（根据权限过期时间判断是否为活动权限）
            var activity = ActivityInfo.Instance;
            if (activity.IsActive && PermissionExpiration.HasValue)
            {
                // 如果权限过期时间接近活动结束时间（误差在1小时内），认为是活动权限
                if (Math.Abs((PermissionExpiration.Value - activity.EndTime).TotalHours) < 1)
                {
                    var remaining = activity.EndTime - DateTime.Now;
                    if (remaining.TotalDays >= 1) return $"免费体验剩余 {(int)remaining.TotalDays} 天";
                    else if (remaining.TotalHours >= 1) return $"免费体验剩余 {(int)remaining.TotalHours} 小时";
                    else if (remaining.TotalMinutes >= 1) return $"免费体验剩余 {(int)remaining.TotalMinutes} 分钟";
                    else return "免费体验即将结束";
                }
            }

            // 显示剩余时间
            var timeSpan = PermissionExpiration.Value - DateTime.Now;
            if (timeSpan.TotalSeconds < 0) return "已过期";
            else if (timeSpan.TotalDays >= 1) return $"剩余 {(int)timeSpan.TotalDays} 天";
            else if (timeSpan.TotalHours >= 1) return $"剩余 {(int)timeSpan.TotalHours} 小时";
            else return $"剩余 {(int)timeSpan.TotalMinutes} 分钟";
        }
    }

    /// <summary>
    /// 是否显示活动提示
    /// </summary>
    public bool ShowActivityNotice => ActivityInfo.Instance.IsActive && Permission <= 1;

    /// <summary>
    /// 用户头像
    /// </summary>
    public BitmapFrame? HeadImg
    {
        get
        {
            try
            {
                return BitmapFrame.Create(
                    new Uri($"http://thirdqq.qlogo.cn/g?b=qq&nk={Uin}&s=100"),
                    BitmapCreateOptions.IgnoreColorProfile,
                    BitmapCacheOption.OnDemand);
            }
            catch
            {
                return null;
            }
        }
    }
    #endregion

    #region Session Management
    private BnszsSession? _session;

    /// <summary>
    /// 用户会话
    /// </summary>
    public BnszsSession? Session => _session;

    /// <summary>
    /// 是否已登录（有有效会话）
    /// </summary>
    public bool IsLoggedIn => _session != null;

    /// <summary>
    /// 会话状态变化事件
    /// </summary>
    public event EventHandler<bool>? SessionStateChanged;

    /// <summary>
    /// 心跳失败事件
    /// </summary>
    public event EventHandler? HeartbeatFailed;

    /// <summary>
    /// 设置用户会话
    /// </summary>
    /// <param name="session">会话实例</param>
    public void SetSession(BnszsSession session)
    {
        // 清理旧会话
        ClearSession();

        // 设置新会话
        _session = session;
        _session.HeartbeatFailed += OnHeartbeatFailed;

        Debug.WriteLine($"[INFO] 用户会话已设置: {Uin}");

        // 触发会话状态变化事件
        SessionStateChanged?.Invoke(this, true);

        // 自动刷新签到状态（登录时不显示错误消息，避免干扰登录流程）
        _ = RefreshSignInStatusAsync();
    }

    /// <summary>
    /// 清理用户会话
    /// </summary>
    public void ClearSession()
    {
        if (_session != null)
        {
            try
            {
                Debug.WriteLine("[INFO] 开始清理用户会话");

                // 取消事件订阅
                _session.HeartbeatFailed -= OnHeartbeatFailed;

                // 释放会话资源
                _session.Dispose();
                _session = null;

                Debug.WriteLine("[INFO] 用户会话已清理");

                // 清空签到状态
                ClearSignInStatus();

                // 触发会话状态变化事件
                SessionStateChanged?.Invoke(this, false);
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"[ERROR] 清理用户会话时发生异常: {ex.Message}");
                // 即使发生异常，也要确保会话被设置为null
                _session = null;
            }
        }
    }

    /// <summary>
    /// 异步注销用户会话
    /// </summary>
    public async Task LogoutAsync()
    {
        if (_session != null)
        {
            _session.HeartbeatFailed -= OnHeartbeatFailed;

            // 发送注销请求到服务器，设置超时时间
            try
            {
                using var cts = new CancellationTokenSource(TimeSpan.FromSeconds(2));
                await _session.LogoutAsync().WaitAsync(cts.Token);
            }
            catch (OperationCanceledException)
            {
                Debug.WriteLine("[WARNING] 注销请求超时");
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"[ERROR] 注销请求失败: {ex.Message}");
            }

            _session.Dispose();
            _session = null;

            Debug.WriteLine("[INFO] 用户会话已注销并清理");

            SessionStateChanged?.Invoke(this, false);
        }
    }

    /// <summary>
    /// 心跳失败处理
    /// </summary>
    private void OnHeartbeatFailed(object? sender, EventArgs e)
    {
        Debug.WriteLine("[WARNING] 心跳失败，触发心跳失败事件");

        // 清理会话
        ClearSession();

        // 触发心跳失败事件，让UI层处理
        HeartbeatFailed?.Invoke(this, EventArgs.Empty);
    }

    /// <summary>
    /// 释放资源
    /// </summary>
    public void Dispose()
    {
        try
        {
            Debug.WriteLine("[INFO] 开始释放User资源");

            // 清理会话
            ClearSession();

            // 清理事件订阅
            SessionStateChanged = null;
            HeartbeatFailed = null;

            Debug.WriteLine("[INFO] User资源释放完成");
        }
        catch (Exception ex)
        {
            Debug.WriteLine($"[ERROR] 释放User资源时发生异常: {ex.Message}");
        }
    }
    #endregion

    #region Sign In
    private bool _isSignedToday;
    private uint _totalSignCount;
    private bool _canSign;
    private string? _lastSignInError;

    /// <summary>
    /// 今日是否已签到
    /// </summary>
    public bool IsSignedToday
    {
        get => _isSignedToday;
        private set
        {
            if (_isSignedToday != value)
            {
                _isSignedToday = value;
                OnPropertyChanged();
                OnPropertyChanged(nameof(SignInStatusText)); // 通知签到状态文本更新
            }
        }
    }

    /// <summary>
    /// 连签天数
    /// </summary>
    public uint TotalSignCount
    {
        get => _totalSignCount;
        private set
        {
            if (_totalSignCount != value)
            {
                _totalSignCount = value;
                OnPropertyChanged();
            }
        }
    }

    /// <summary>
    /// 是否可以签到
    /// </summary>
    public bool CanSign
    {
        get => _canSign;
        private set
        {
            if (_canSign != value)
            {
                _canSign = value;
                OnPropertyChanged();
                OnPropertyChanged(nameof(SignInStatusText)); // 通知签到状态文本更新
            }
        }
    }

    /// <summary>
    /// 签到状态文本
    /// </summary>
    public string SignInStatusText
    {
        get
        {
            if (IsSignedToday)
                return "今日已签到";
            else if (CanSign)
                return "立即签到";
            else
                return _lastSignInError ?? "暂无活动";
        }
    }

    /// <summary>
    /// 刷新签到状态
    /// </summary>
    public async Task<(bool success, string? errorMessage)> RefreshSignInStatusAsync()
    {
        if (_session == null)
        {
            Debug.WriteLine("[WARNING] 用户会话为空，无法刷新签到状态");
            return (false, "用户会话为空");
        }

        try
        {
            var response = await _session.GetLuckyStatusAsync();
            if (response != null)
            {
                if (response.ErrorCode == 0)
                {
                    UpdateSignInStatus(response);
                    _lastSignInError = null; // 清空错误消息
                    return (true, null);
                }
                else
                {
                    var errorMessage = response.ErrorMessage ?? "获取签到状态失败";
                    Debug.WriteLine($"获取签到状态失败: {errorMessage}");

                    // 如果是"已签到"类型的错误，缓存为不可用状态
                    if (errorMessage.Contains("已经签到") || errorMessage.Contains("已签到"))
                    {
                        CanSign = false;
                        IsSignedToday = true;
                        _lastSignInError = null; // 已签到状态不需要显示错误消息
                    }
                    else
                    {
                        // 对于其他类型的错误（如"暂无开启的活动"），清空签到状态并存储错误消息
                        ClearSignInStatus();
                        _lastSignInError = errorMessage;
                    }

                    return (false, errorMessage);
                }
            }

            Debug.WriteLine("获取签到状态失败: 响应为空");
            return (false, "服务器无响应");
        }
        catch (Exception ex)
        {
            Debug.WriteLine($"刷新签到状态异常: {ex.Message}");
            return (false, $"网络异常: {ex.Message}");
        }
    }

    /// <summary>
    /// 执行签到
    /// </summary>
    public async Task<(bool Success, string? Message)> SignInAsync()
    {
        if (_session == null) return (false, "用户会话为空");
        if (!CanSign) return (false, "今日已签到或不满足签到条件");

        try
        {
            var response = await _session.LuckyDrawAsync();
            if (response != null)
            {
                if (response.ErrorCode == 0)
                {
                    // 签到成功，检查是否包含权限更新信息
                    UpdatePermissionInfo(response.Permission, response.PermissionExpiration);

                    _ = await RefreshSignInStatusAsync();
                    return (true, response.RewardMessage);
                }
                else
                {
                    // 签到失败，如果是"已签到"类型的错误，缓存为不可用状态
                    var errorMessage = response.ErrorMessage ?? "签到失败";
                    if (errorMessage.Contains("已经签到") || errorMessage.Contains("已签到"))
                    {
                        CanSign = false;
                        IsSignedToday = true;
                    }

                    return (false, errorMessage);
                }
            }

            return (false, "签到失败: 服务器无响应");
        }
        catch (Exception ex)
        {
            Debug.WriteLine($"签到异常: {ex.Message}");
            return (false, $"签到异常: {ex.Message}");
        }
    }

    /// <summary>
    /// 激活CDKEY
    /// </summary>
    /// <param name="cdkey">要激活的CDKEY</param>
    /// <returns>激活结果</returns>
    public async Task<(bool Success, string Message)> ActivateCDKeyAsync(string cdkey)
    {
        ArgumentNullException.ThrowIfNull(_session);

        if (string.IsNullOrWhiteSpace(cdkey))
        {
            return (false, "口令码不能为空");
        }

        try
        {
            // 第一阶段：检查是否需要群验证
            var response = await _session.ActivateCDKeyAsync(cdkey, false);
            if (response == null) return (false, "口令码激活失败: 服务器无响应");

            // 处理第一阶段响应
            var (result, needVerification) = ProcessCDKeyResponse(response);
            if (!needVerification) return result;

            // 需要群验证，检查用户是否在指定群中
            if (!UserLogin2.Groups.Contains(response.Group!.Value))
            {
                return (false, "您不符合使用当前口令码需要的条件");
            }

            // 第二阶段：发送群验证通过的请求
            var verifiedResponse = await _session.ActivateCDKeyAsync(cdkey, true);
            if (verifiedResponse == null) return (false, "口令码激活失败: 服务器无响应");

            // 处理第二阶段响应
            return ProcessCDKeyResponse(verifiedResponse).result;
        }
        catch (Exception ex)
        {
            return (false, $"口令码激活失败: {ex.Message}");
        }
    }

    /// <summary>
    /// 处理CDKey激活响应
    /// </summary>
    /// <param name="response">服务器响应</param>
    /// <returns>处理结果和是否需要群验证</returns>
    private ((bool Success, string Message) result, bool needVerification) ProcessCDKeyResponse(CDKeyActivatePacket response)
    {
        if (response.ErrorCode != 0)
        {
            return ((false, response.ErrorMessage!), false);
        }

        // 检查是否需要群验证
        if (response.Group.HasValue) return ((false, ""), true); 

        // 激活成功，检查是否包含权限更新信息
        UpdatePermissionInfo(response.Permission, response.PermissionExpiration);

        return ((true, "口令码激活成功"), false);
    }

    /// <summary>
    /// 更新权限信息
    /// </summary>
    /// <param name="permission">新的权限级别</param>
    /// <param name="permissionExpiration">新的权限过期时间</param>
    internal void UpdatePermissionInfo(byte? permission, long? permissionExpiration)
    {
        // 两个值都必须存在
        if (permission is null || permissionExpiration is null) return;

        // 先更新权限过期时间，避免在更新权限等级时触发过期检查
        if (permissionExpiration == -1)
        {
            // 永久权限
            PermissionExpiration = DateTime.MaxValue;
        }
        else if (permissionExpiration > 0)
        {
            PermissionExpiration = DateTimeOffset.FromUnixTimeSeconds(permissionExpiration.Value).DateTime.ToLocalTime();
        }
        else
        {
            PermissionExpiration = null;
        }

        // 然后更新权限级别（此时过期时间已经是最新的）
        Permission = permission.Value;
        Debug.WriteLine($"[INFO] 权限信息已更新: Permission={Permission}, PermissionExpiration={PermissionExpiration}");
    }

    /// <summary>
    /// 更新签到状态
    /// </summary>
    private void UpdateSignInStatus(LuckyStatusPacket response)
    {
        TotalSignCount = response.Point;  // 使用连签天数
        CanSign = response.AvailableCount > 0; // 根据可用次数判断是否可以签到
        IsSignedToday = response.AvailableCount == 0; // 如果可用次数为0，说明今日已签到
        OnPropertyChanged(nameof(SignInStatusText)); // 通知状态文本更新
    }

    /// <summary>
    /// 清空签到状态
    /// </summary>
    private void ClearSignInStatus()
    {
        IsSignedToday = false;
        TotalSignCount = 0;
        CanSign = false;
        // 注意：不在这里清空 _lastSignInError，因为我们需要显示错误消息
        OnPropertyChanged(nameof(SignInStatusText)); // 手动通知状态文本更新
    }
    #endregion

    #region INotifyPropertyChanged
    public event PropertyChangedEventHandler? PropertyChanged;

    protected virtual void OnPropertyChanged([CallerMemberName] string? propertyName = null)
    {
        PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
    }
    #endregion
}
