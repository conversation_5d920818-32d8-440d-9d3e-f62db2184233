<?php

namespace app\manage\controller\manage;

use app\common\controller\AdminBaseController;
use think\facade\Cache;
use think\facade\Db;
use think\facade\View;
use app\manage\model\UserAdmin as UserAdmin;

/**
 * 风险控制管理控制器
 * 需要管理员权限
 */
class RiskControl extends AdminBaseController
{
    /**
     * 获取管理员信息
     */
    private function getAdminInfo()
    {
        static $adminInfo = null;
        if ($adminInfo === null) {
            $adminInfo = UserAdmin::where('uid', $this->getAdminId())->find();
            if (!$adminInfo) {
                $adminInfo = [
                    'uid' => $this->getAdminId(),
                    'username' => 'admin',
                    'super' => 0,
                    'power' => ''
                ];
            }
        }
        return $adminInfo;
    }

    /**
     * 检查风控权限
     */
    private function checkRiskControlPermission() {
        $adminInfo = $this->getAdminInfo();

        // 超级管理员拥有所有权限
        if($adminInfo['super'] == 1) {
            return true;
        }

        // 检查是否有风控相关权限 (14, 15, 16)
        $powers = explode(',', $adminInfo['power']);
        $riskControlPowers = [14, 15, 16]; // 风控查看、管理、配置权限
        
        foreach($riskControlPowers as $power) {
            if(in_array($power, $powers)) {
                return true;
            }
        }
        
        return false;
    }

    /**
     * 检查特定权限
     */
    private function checkPermission($powerID) {
        $adminInfo = $this->getAdminInfo();

        if($adminInfo['super'] == 1) {
            return true;
        }

        $powers = explode(',', $adminInfo['power']);
        return in_array($powerID, $powers);
    }

    /**
     * 风控仪表板
     */
    public function index() {
        if($this->request->isPost()) {
            // 处理POST请求
            $action = $this->request->param('action', '');

            switch($action) {
                case 'processEvent':
                    return $this->processEvent();
                case 'batchProcessUsers':
                    return $this->batchProcessUsers();
                case 'saveConfig':
                    return $this->saveConfig();
                case 'recordEvent':
                    return $this->recordEvent();
                case 'getStats':
                    return $this->getStatsApi();
                case 'getEvents':
                    return $this->getEventsApi();
                case 'updateEventStatus':
                    return $this->updateEventStatus();
                case 'checkDeviceRisk':
                    return $this->checkDeviceRisk();
                case 'health':
                    return $this->health();
                default:
                    return json(['code' => 0, 'msg' => '未知操作']);
            }
        }

        try {
            // 获取今日统计
            $today = date('Y-m-d');
            $todayStart = $today . ' 00:00:00';
            $todayEnd = $today . ' 23:59:59';

            // 用户状态统计（使用实际存在的表）
            $auditUsers = Db::name('user')->where('status', 3)->count(); // 审核状态用户
            $bannedUsers = Db::name('user')->where('status', '>', 0)->count(); // 所有异常状态用户
            $totalUsers = Db::name('user')->count(); // 总用户数
            $normalUsers = Db::name('user')->where('status', 0)->count(); // 正常用户

            // 尝试获取风险事件统计（如果表不存在则使用默认值）
            $totalEvents = 0;
            $pendingEvents = 0;
            $highSeverityEvents = 0;
            $mediumSeverityEvents = 0;
            $recentEvents = [];

            try {
                $totalEvents = Db::name('risk_events')
                    ->where('created_at', 'between', [$todayStart, $todayEnd])
                    ->count();

                $pendingEvents = Db::name('risk_events')
                    ->where('created_at', 'between', [$todayStart, $todayEnd])
                    ->where('status', 'pending')
                    ->count();

                $highSeverityEvents = Db::name('risk_events')
                    ->where('created_at', 'between', [$todayStart, $todayEnd])
                    ->where('severity', 'high')
                    ->count();

                $mediumSeverityEvents = Db::name('risk_events')
                    ->where('created_at', 'between', [$todayStart, $todayEnd])
                    ->where('severity', 'medium')
                    ->count();

                // 最近的风险事件
                $recentEvents = Db::name('risk_events')
                    ->order('created_at desc')
                    ->limit(10)
                    ->select();
            } catch (\Exception $e) {
                // 如果risk_events表不存在，使用默认值
                trace('risk_events表不存在或查询失败: ' . $e->getMessage(), 'info');
            }

            $data = [
                'stats' => [
                    'total_events' => $totalEvents,
                    'pending_events' => $pendingEvents,
                    'high_severity' => $highSeverityEvents,
                    'medium_severity' => $mediumSeverityEvents,
                    'audit_users' => $auditUsers,
                    'banned_users' => $bannedUsers,
                    'total_users' => $totalUsers,
                    'normal_users' => $normalUsers
                ],
                'recent_events' => $recentEvents,
                'admin_info' => $this->getAdminInfo()
            ];

            return View::fetch('/admin/risk_dashboard', $data);

        } catch (\Exception $e) {
            abort(500, '获取风控数据失败: ' . $e->getMessage());
        }
    }

    /**
     * 风险事件列表
     */
    public function events() {
        try {
            $page = $this->request->param('page', 1);
            $pageSize = $this->request->param('page_size', 20);
            $status = $this->request->param('status', '');
            $severity = $this->request->param('severity', '');

            $where = [];
            if($status) {
                $where['status'] = $status;
            }
            if($severity) {
                $where['severity'] = $severity;
            }

            $events = Db::name('risk_events')
                ->where($where)
                ->order('created_at desc')
                ->paginate($pageSize, false, ['page' => $page]);

            $data = [
                'events' => $events,
                'status' => $status,
                'severity' => $severity,
                'admin_info' => $this->getAdminInfo(),
                'can_manage' => $this->checkPermission(15) // 风控管理权限
            ];

            return View::fetch('/admin/risk_events', $data);

        } catch (\Exception $e) {
            abort(500, '获取风险事件失败: ' . $e->getMessage());
        }
    }

    /**
     * 处理风险事件（私有方法）
     */
    private function processEvent() {
        if(!$this->checkPermission(15)) {
            abort(403, '您没有风控管理权限');
        }

        try {
            $eventId = $this->request->param('event_id');
            $action = $this->request->param('action');
            $adminNote = $this->request->param('admin_note', '');

            if(!$eventId || !$action) {
                return json(['code' => 0, 'msg' => '参数错误']);
            }

            // 获取事件信息
            $event = Db::name('risk_events')->where('id', $eventId)->find();
            if(!$event) {
                return json(['code' => 0, 'msg' => '风险事件不存在']);
            }

            // 开始事务
            Db::startTrans();

            try {
                // 更新事件状态
                $updateData = [
                    'status' => $action == 'ignore' ? 'ignored' : 'processed',
                    'updated_at' => date('Y-m-d H:i:s')
                ];

                Db::name('risk_events')->where('id', $eventId)->update($updateData);

                // 如果是批准操作，恢复用户状态
                if($action == 'approve' && $event['qq_numbers']) {
                    $this->restoreUsersFromAudit($event['qq_numbers'], '管理员批准: ' . $adminNote);
                }

                // 记录管理员操作日志
                $this->logAdminAction('process_risk_event', "处理风控事件: {$action}", [
                    'event_id' => $eventId,
                    'action' => $action,
                    'admin_note' => $adminNote,
                    'event_type' => $event['event_type']
                ]);

                Db::commit();
                return json(['code' => 1, 'msg' => '处理成功']);

            } catch (Exception $e) {
                Db::rollback();
                throw $e;
            }

        } catch (Exception $e) {
            return json(['code' => 0, 'msg' => '处理失败: ' . $e->getMessage()]);
        }
    }

    /**
     * 批量处理用户（私有方法）
     */
    private function batchProcessUsers() {
        if(!$this->checkPermission(15)) {
            return json(['code' => 0, 'msg' => '您没有风控管理权限']);
        }

        try {
            $qqNumbers = $this->request->param('qq_numbers');
            $action = $this->request->param('action');
            $reason = $this->request->param('reason', '');

            if(!$qqNumbers || !$action) {
                return json(['code' => 0, 'msg' => '参数错误']);
            }

            if(is_string($qqNumbers)) {
                $qqNumbers = explode(',', $qqNumbers);
            }

            // 开始事务
            Db::startTrans();

            try {
                foreach($qqNumbers as $qqNumber) {
                    $qqNumber = trim($qqNumber);
                    if(!$qqNumber) continue;

                    $user = Db::name('user')->where('uin', $qqNumber)->find();
                    if(!$user) continue;

                    $newStatus = 0; // 默认恢复正常
                    switch($action) {
                        case 'approve':
                            $newStatus = 0;
                            break;
                        case 'audit':
                            $newStatus = 3;
                            break;
                        case 'temp_ban':
                            $newStatus = 1;
                            break;
                        case 'permanent_ban':
                            $newStatus = 2;
                            break;
                    }

                    // 更新用户状态
                    Db::name('user')->where('uin', $qqNumber)->update(['status' => $newStatus]);

                    // 记录用户日志
                    Db::name('user_log')->insert([
                        'uid' => $user['uid'],
                        'type' => 'admin_action',
                        'extra' => "管理员操作: {$action}, 原因: {$reason}",
                        'time' => date('Y-m-d H:i:s')
                    ]);
                }

                // 记录管理员操作日志
                $this->logAdminAction('batch_process_users', "批量处理用户: {$action}", [
                    'qq_numbers' => $qqNumbers,
                    'action' => $action,
                    'reason' => $reason
                ]);

                Db::commit();
                return json(['code' => 1, 'msg' => '批量处理成功']);

            } catch (Exception $e) {
                Db::rollback();
                throw $e;
            }

        } catch (Exception $e) {
            return json(['code' => 0, 'msg' => '批量处理失败: ' . $e->getMessage()]);
        }
    }

    /**
     * 恢复用户状态
     */
    private function restoreUsersFromAudit($qqNumbersStr, $reason) {
        // 解析QQ号字符串
        $qqNumbersStr = trim($qqNumbersStr, '[]');
        $qqNumbers = explode(',', $qqNumbersStr);

        foreach($qqNumbers as $qqNumber) {
            $qqNumber = trim($qqNumber, ' "\'');
            if(!$qqNumber) continue;

            $user = Db::name('user')->where('uin', $qqNumber)->find();
            if($user && $user['status'] == 3) {
                // 恢复为正常状态
                Db::name('user')->where('uin', $qqNumber)->update(['status' => 0]);

                // 记录用户日志
                Db::name('user_log')->insert([
                    'uid' => $user['uid'],
                    'type' => 'risk_control_restore',
                    'extra' => $reason,
                    'time' => date('Y-m-d H:i:s')
                ]);
            }
        }
    }

    /**
     * 风控配置
     */
    public function config() {
        if(!$this->checkPermission(16)) {
            abort(403, '您没有风控配置权限');
        }

        try {
            // 从数据库读取配置
            $configData = Db::name('risk_control_config')->select();
            $config = [];

            // 转换为关联数组
            foreach($configData as $item) {
                $config[$item['config_key']] = $item['config_value'];
            }

            // 设置默认值
            if(empty($config)) {
                $config = [
                    'max_qq_per_device_per_day' => '3',
                    'max_qq_per_ip_per_day' => '3',
                    'max_devices_per_qq_per_day' => '3',
                    'max_login_attempts_per_hour' => '20',
                    'enable_new_device_check' => '1',
                    'enable_location_check' => '0',
                ];
            }

            $data = [
                'config' => $config,
                'admin_info' => $this->getAdminInfo()
            ];

            return View::fetch('/admin/risk_config', $data);

        } catch (\Exception $e) {
            abort(500, '操作失败: ' . $e->getMessage());
        }
    }

    /**
     * 保存风控配置（私有方法）
     */
    private function saveConfig() {
        if(!$this->checkPermission(16)) {
            return json(['code' => 0, 'msg' => '您没有风控配置权限']);
        }

        try {
            // 保存配置到数据库
            $config = [
                'max_qq_per_device_per_day' => $this->request->param('max_qq_per_device_per_day', 5),
                'max_qq_per_ip_per_day' => $this->request->param('max_qq_per_ip_per_day', 10),
                'max_devices_per_qq_per_day' => $this->request->param('max_devices_per_qq_per_day', 3),
                'max_login_attempts_per_hour' => $this->request->param('max_login_attempts_per_hour', 20),
                'enable_new_device_check' => $this->request->param('enable_new_device_check', 1),
                'enable_location_check' => $this->request->param('enable_location_check', 0),
            ];

            // 保存到数据库
            foreach($config as $key => $value) {
                Db::name('risk_control_config')
                    ->where('config_key', $key)
                    ->update([
                        'config_value' => (string)$value,
                        'updated_at' => date('Y-m-d H:i:s')
                    ]);
            }

            // 记录管理员操作日志
            $this->logAdminAction('update_risk_config', '更新风控配置', $config);

            return json(['code' => 1, 'msg' => '配置保存成功']);

        } catch (\Exception $e) {
            return json(['code' => 0, 'msg' => '操作失败: ' . $e->getMessage()]);
        }
    }








    /**
     * 记录风险事件（API方法）
     */
    private function recordEvent() {
        try {
            $data = $this->request->param();

            // 验证必要参数
            if(!isset($data['event_type']) || !isset($data['severity'])) {
                return json(['code' => 0, 'msg' => '参数错误']);
            }

            // 插入风险事件
            $eventData = [
                'event_type' => $data['event_type'],
                'severity' => $data['severity'],
                'user_id' => $data['user_id'] ?? 0,
                'device_id' => $data['device_id'] ?? '',
                'ip_address' => $data['ip_address'] ?? $this->request->ip(),
                'description' => $data['description'] ?? '',
                'metadata' => isset($data['metadata']) ? json_encode($data['metadata']) : '{}',
                'status' => 'pending',
                'created_at' => date('Y-m-d H:i:s')
            ];

            $eventId = Db::name('risk_events')->insertGetId($eventData);

            return json(['code' => 1, 'msg' => '事件记录成功', 'data' => ['event_id' => $eventId]]);

        } catch (\Exception $e) {
            return json(['code' => 0, 'msg' => '记录失败: ' . $e->getMessage()]);
        }
    }

    /**
     * 获取风控统计（API方法）
     */
    private function getStatsApi() {
        try {
            $timeRange = $this->request->param('time_range', 'today');

            $stats = $this->getRiskStats($timeRange);

            return json(['code' => 1, 'msg' => '获取成功', 'data' => $stats]);

        } catch (\Exception $e) {
            return json(['code' => 0, 'msg' => '获取失败: ' . $e->getMessage()]);
        }
    }

    /**
     * 获取风险事件列表（API方法）
     */
    private function getEventsApi() {
        try {
            $page = $this->request->param('page', 1);
            $limit = $this->request->param('limit', 20);
            $status = $this->request->param('status', '');
            $severity = $this->request->param('severity', '');

            $where = [];
            if ($status) {
                $where['status'] = $status;
            }
            if ($severity) {
                $where['severity'] = $severity;
            }

            $events = Db::name('risk_events')
                ->where($where)
                ->order('created_at desc')
                ->page($page, $limit)
                ->select();

            $total = Db::name('risk_events')->where($where)->count();

            return json([
                'code' => 1,
                'msg' => '获取成功',
                'data' => [
                    'events' => $events,
                    'total' => $total,
                    'page' => $page,
                    'limit' => $limit
                ]
            ]);

        } catch (\Exception $e) {
            return json(['code' => 0, 'msg' => '获取失败: ' . $e->getMessage()]);
        }
    }

    /**
     * 更新事件状态（API方法）
     */
    private function updateEventStatus() {
        try {
            $eventId = $this->request->param('event_id');
            $status = $this->request->param('status');
            $remark = $this->request->param('remark', '');

            if (!$eventId || !$status) {
                return json(['code' => 0, 'msg' => '参数错误']);
            }

            $result = Db::name('risk_events')
                ->where('id', $eventId)
                ->update([
                    'status' => $status,
                    'remark' => $remark,
                    'processed_at' => date('Y-m-d H:i:s'),
                    'processed_by' => $this->getAdminId()
                ]);

            if ($result) {
                return json(['code' => 1, 'msg' => '状态更新成功']);
            } else {
                return json(['code' => 0, 'msg' => '状态更新失败']);
            }

        } catch (\Exception $e) {
            return json(['code' => 0, 'msg' => '更新失败: ' . $e->getMessage()]);
        }
    }

    /**
     * 检查设备风险（API方法）
     */
    private function checkDeviceRisk() {
        try {
            $deviceId = $this->request->param('device_id');
            $userId = $this->request->param('user_id', 0);

            if (!$deviceId) {
                return json(['code' => 0, 'msg' => '设备ID不能为空']);
            }

            // 检查设备风险等级
            $riskLevel = $this->calculateDeviceRisk($deviceId, $userId);

            return json([
                'code' => 1,
                'msg' => '检查完成',
                'data' => [
                    'device_id' => $deviceId,
                    'user_id' => $userId,
                    'risk_level' => $riskLevel,
                    'timestamp' => time()
                ]
            ]);

        } catch (\Exception $e) {
            return json(['code' => 0, 'msg' => '检查失败: ' . $e->getMessage()]);
        }
    }

    /**
     * 健康检查（API方法）
     */
    private function health() {
        try {
            // 检查数据库连接
            $eventCount = Db::name('risk_events')->count();

            return json([
                'code' => 1,
                'msg' => '服务正常',
                'data' => [
                    'status' => 'healthy',
                    'event_count' => $eventCount,
                    'timestamp' => time()
                ]
            ]);

        } catch (\Exception $e) {
            return json(['code' => 0, 'msg' => '服务异常: ' . $e->getMessage()]);
        }
    }

    /**
     * 计算设备风险等级
     */
    private function calculateDeviceRisk($deviceId, $userId) {
        // 简化的风险计算逻辑
        $riskScore = 0;

        // 检查最近的风险事件
        $recentEvents = Db::name('risk_events')
            ->where('device_id', $deviceId)
            ->where('created_at', '>', date('Y-m-d H:i:s', strtotime('-7 days')))
            ->count();

        $riskScore += $recentEvents * 10;

        // 根据分数确定风险等级
        if ($riskScore >= 50) {
            return 'high';
        } elseif ($riskScore >= 20) {
            return 'medium';
        } else {
            return 'low';
        }
    }

    /**
     * 获取风控统计数据
     */
    private function getRiskStats($timeRange) {
        $where = [];

        switch ($timeRange) {
            case 'today':
                $where[] = ['created_at', '>=', date('Y-m-d 00:00:00')];
                break;
            case 'week':
                $where[] = ['created_at', '>=', date('Y-m-d 00:00:00', strtotime('-7 days'))];
                break;
            case 'month':
                $where[] = ['created_at', '>=', date('Y-m-01 00:00:00')];
                break;
        }

        $totalEvents = Db::name('risk_events')->where($where)->count();
        $highRiskEvents = Db::name('risk_events')->where($where)->where('severity', 'high')->count();
        $pendingEvents = Db::name('risk_events')->where($where)->where('status', 'pending')->count();

        return [
            'total_events' => $totalEvents,
            'high_risk_events' => $highRiskEvents,
            'pending_events' => $pendingEvents,
            'time_range' => $timeRange
        ];
    }

    /**
     * 记录管理员操作日志
     */
    protected function logAdminAction($action, $description, $data = []) {
        try {
            Db::name('admin_logs')->insert([
                'admin_id' => $this->getAdminId(),
                'action' => $action,
                'description' => $description,
                'data' => json_encode($data, JSON_UNESCAPED_UNICODE),
                'ip' => $this->request->ip(),
                'user_agent' => $this->request->header('User-Agent'),
                'created_at' => date('Y-m-d H:i:s')
            ]);
        } catch (\Exception $e) {
            // 记录日志失败不影响主流程
            trace('Failed to log admin action: ' . $e->getMessage(), 'error');
        }
    }
}
