<?php
// +----------------------------------------------------------------------
// | Manage应用路由配置
// +----------------------------------------------------------------------

use think\facade\Route;

// 管理后台基础路由
Route::rule('/', 'Manage/Index');
Route::rule('/login', 'Manage/Login');
Route::rule('/logout', 'Manage/Logout');
Route::rule('/help', 'Manage/Help');
Route::rule('/choose', 'Manage/Choose');
Route::rule('/liars', 'Manage/Liars');
Route::rule('/liarpost', 'Manage/LiarPost');
Route::rule('/changelog', 'Manage/ChangeLog');
Route::rule('/sendcode', 'Manage/SendCode');
Route::rule('/register', 'Manage/Register');
Route::rule('/image', 'Manage/Image');

// 用户中心路由
Route::rule('/center', 'manage/Center/main');
Route::rule('/draw', 'manage/Center/Draw');
Route::rule('/profile', 'manage/Profile/main');

Route::group('admin', function() {
    Route::rule('/check', 'manage.AdminCheck/index');
    Route::rule('/cdkey', 'manage.CdkeyManage/index');
    Route::rule('/users', 'manage.UserManage/index');
    Route::rule('/profiles', 'manage.ProfileManage/index');
    Route::rule('/downloads', 'manage.DownloadManage/index');
    Route::rule('/deleteDownload', 'manage.DownloadManage/delete');

    // 风控管理路由
    Route::group('risk', function() {
        Route::rule('/', 'manage.RiskControl/index');
        Route::rule('/events', 'manage.RiskControl/events');
        Route::rule('/config', 'manage.RiskControl/config');
    });

    // 签到管理路由
    Route::group('signin', function() {
        Route::rule('/', 'manage.SigninManage/index');
    });

    // 在线用户统计管理路由
    Route::group('online', function() {
        Route::rule('/', 'manage.OnlineDashboard/index');
    });

    // 公告管理路由
    Route::group('announcement', function() {
        Route::rule('/', 'manage.Announcement/index');
        Route::rule('/add', 'manage.Announcement/add');
        Route::rule('/edit/:id', 'manage.Announcement/edit');
    });

    // 更新配置管理路由
    Route::group('update-config', function() {
        Route::rule('/', 'manage.UpdateConfig/index');
        Route::rule('/edit', 'manage.UpdateConfig/edit');
        Route::rule('/groups/:app_name', 'manage.UpdateConfig/groups');
    });
});

// API接口路由（整合到管理控制器中）
Route::group('api', function() {
    // 在线统计API - 通过action参数区分功能
    Route::rule('/online/current', 'manage.OnlineDashboard/index?action=current', 'POST');
    Route::rule('/online/health', 'manage.OnlineDashboard/index?action=health', 'POST');

    // 风控API - 通过action参数区分功能
    Route::rule('/risk/record', 'manage.RiskControl/index?action=recordEvent', 'POST');
    Route::rule('/risk/stats', 'manage.RiskControl/index?action=getStats', 'POST');
    Route::rule('/risk/events', 'manage.RiskControl/index?action=getEvents', 'POST');
    Route::rule('/risk/update', 'manage.RiskControl/index?action=updateEventStatus', 'POST');
    Route::rule('/risk/check', 'manage.RiskControl/index?action=checkDeviceRisk', 'POST');
    Route::rule('/risk/health', 'manage.RiskControl/index?action=health', 'POST');

    // 更新配置API - 通过action参数区分功能
    Route::rule('/update', 'manage.UpdateConfig/index?action=list', 'POST');
    Route::rule('/update/getForceUpdateStatus', 'manage.UpdateConfig/index?action=getForceUpdateStatus', 'POST');
    Route::rule('/update/setForceUpdate', 'manage.UpdateConfig/index?action=setForceUpdate', 'POST');

    // 公告API - 通过action参数区分功能
    Route::rule('/announcement/list', 'manage.Announcement/index?action=list', 'POST');
    Route::rule('/announcement/checkUpdate', 'manage.Announcement/index?action=checkUpdate', 'POST');
    Route::rule('/announcement/detail', 'manage.Announcement/detail');
    Route::rule('/announcement/stats', 'manage.Announcement/index?action=stats', 'POST');
    Route::rule('/announcement/health', 'manage.Announcement/index?action=health', 'POST');
});
