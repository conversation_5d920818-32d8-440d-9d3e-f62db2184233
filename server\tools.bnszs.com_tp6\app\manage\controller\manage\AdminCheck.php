<?php

namespace app\manage\controller\manage;

use app\common\controller\AdminBaseController;
use think\facade\View;

/**
 * 管理员检查控制器
 */
class AdminCheck extends AdminBaseController
{
    /**
     * 管理员功能统一入口
     */
    public function index() 
    {
        if($this->request->isPost()) {
            // 处理POST请求 - 暂时不支持POST操作
            return $this->error('POST操作暂不支持');
        }

        // 默认显示管理员检查页面
        $status = $this->request->param('status', 0);
        $permission = $this->request->param('permission', 0);

        // 简单返回管理员信息，或者重定向到用户管理页面
        return redirect('/manage/admin/users');
    }

    /**
     * 获取管理员信息
     */
    public function getAdminInfo()
    {
        try {
            $adminInfo = [
                'admin_id' => $this->getAdminId(),
                'login_time' => $_SESSION['admin_login_time'] ?? null,
                'last_activity' => $_SESSION['admin_last_activity'] ?? null,
                'permissions' => $this->getAdminPermissions()
            ];

            return $this->success($adminInfo, '获取管理员信息成功');
        } catch (\Exception $e) {
            return $this->error('获取管理员信息失败: ' . $e->getMessage());
        }
    }

    /**
     * 获取管理员权限列表（预留接口）
     */
    private function getAdminPermissions()
    {
        // 目前所有管理员都有相同权限，后续可以根据需要扩展
        return [
            'user_manage' => true,
            'cdkey_manage' => true,
            'profile_manage' => true,
            'download_manage' => true,
            'update_config' => true,
            'announcement_manage' => true,
            'risk_control' => true,
            'online_stats' => true
        ];
    }

    /**
     * 检查特定权限
     */
    public function checkPermission()
    {
        $permission = $this->request->param('permission');
        
        if (empty($permission)) {
            return $this->error('权限参数不能为空');
        }

        $hasPermission = $this->hasPermission($permission);
        
        return $this->success([
            'permission' => $permission,
            'has_permission' => $hasPermission
        ], $hasPermission ? '有权限' : '无权限');
    }

    /**
     * 管理员操作日志查看（预留接口）
     */
    public function viewLogs()
    {
        // 预留接口，可以实现管理员操作日志查看功能
        $page = $this->request->param('page', 1);
        $limit = $this->request->param('limit', 20);
        
        // 这里可以实现从日志文件或数据库读取管理员操作日志
        $logs = [
            // 示例数据结构
            // [
            //     'id' => 1,
            //     'admin_id' => $this->getAdminId(),
            //     'action' => 'user_manage',
            //     'description' => '修改用户状态',
            //     'data' => [],
            //     'created_at' => date('Y-m-d H:i:s')
            // ]
        ];

        return $this->success($logs, '获取日志成功');
    }

    /**
     * 系统状态检查
     */
    public function systemStatus()
    {
        try {
            $status = [
                'php_version' => PHP_VERSION,
                'memory_usage' => $this->formatBytes(memory_get_usage(true)),
                'memory_peak' => $this->formatBytes(memory_get_peak_usage(true)),
                'disk_free_space' => $this->formatBytes(disk_free_space('.')),
                'server_time' => date('Y-m-d H:i:s'),
                'timezone' => date_default_timezone_get(),
                'session_status' => session_status() === PHP_SESSION_ACTIVE ? '活跃' : '未活跃',
                'extensions' => [
                    'redis' => extension_loaded('redis') ? '已安装' : '未安装',
                    'curl' => extension_loaded('curl') ? '已安装' : '未安装',
                    'pdo' => extension_loaded('pdo') ? '已安装' : '未安装',
                    'json' => extension_loaded('json') ? '已安装' : '未安装'
                ]
            ];

            return $this->success($status, '获取系统状态成功');
        } catch (\Exception $e) {
            return $this->error('获取系统状态失败: ' . $e->getMessage());
        }
    }

    /**
     * 格式化字节数
     */
    private function formatBytes($bytes, $precision = 2)
    {
        $units = array('B', 'KB', 'MB', 'GB', 'TB');

        for ($i = 0; $bytes > 1024 && $i < count($units) - 1; $i++) {
            $bytes /= 1024;
        }

        return round($bytes, $precision) . ' ' . $units[$i];
    }

    /**
     * 清理会话
     */
    public function clearSession()
    {
        if (!$this->request->isPost()) {
            return $this->error('请求方式错误');
        }

        try {
            // 记录操作
            $this->logAdminAction('clear_session', '清理管理员会话');
            
            // 清理会话
            session_destroy();
            
            return $this->success([], '会话清理成功，请重新登录');
        } catch (\Exception $e) {
            return $this->error('会话清理失败: ' . $e->getMessage());
        }
    }
}
