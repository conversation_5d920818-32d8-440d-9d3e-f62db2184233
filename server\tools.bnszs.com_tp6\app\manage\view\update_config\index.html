{layout name="manage/template" /}

<div class="admin-content">
    <div class="admin-content-body">
        <div class="am-cf am-padding am-padding-bottom-0">
            <div class="am-fl am-cf">
                <strong class="am-text-primary am-text-lg">更新配置管理</strong> /
                <small>Update Config Management</small>
            </div>
        </div>

        <hr>

        <div class="am-g">
            <div class="am-u-sm-12">
                <div class="am-panel am-panel-default">
                    <div class="am-panel-hd">
                        <h3 class="am-panel-title">
                            <i class="am-icon-cog"></i> 更新配置列表
                            <div class="am-fr">
                                <a href="/manage/admin/update-config/edit" class="am-btn am-btn-primary am-btn-xs">
                                    <i class="am-icon-plus"></i> 添加配置
                                </a>
                            </div>
                        </h3>
                    </div>

                    <div class="am-panel-bd">
                        {if isset($configs) && count($configs) > 0}
                        <div class="am-scrollable-horizontal">
                            <table class="am-table am-table-striped am-table-hover am-table-bd am-table-bdrs am-table-centered">
                                <thead>
                                    <tr>
                                        <th>ID</th>
                                        <th>应用名称</th>
                                        <th>当前版本</th>
                                        <th>可执行文件</th>
                                        <th>下载链接</th>
                                        <th>插件版本</th>
                                        <th>测试版本</th>
                                        <th>状态</th>
                                        <th>群组数量</th>
                                        <th>创建时间</th>
                                        <th>操作</th>
                                    </tr>
                                </thead>
                            <tbody>
                                {volist name="configs" id="config"}
                                <tr>
                                    <td>{$config.id}</td>
                                    <td>
                                        <strong class="am-text-primary">{$config.name}</strong>
                                    </td>
                                    <td>
                                        <span class="am-badge am-badge-primary">{$config.version}</span>
                                    </td>
                                    <td><code>{$config.executable_path}</code></td>
                                    <td>
                                        {if $config.url}
                                        <a href="{$config.url}" target="_blank" class="am-text-primary">
                                            <i class="am-icon-download"></i> 下载
                                        </a>
                                        {else/}
                                        <span class="am-text-muted">未设置</span>
                                        {/if}
                                    </td>
                                    <td>
                                        {if $config.plugin_version}
                                        <span class="am-badge am-badge-secondary">{$config.plugin_version}</span>
                                        {else/}
                                        <span class="am-text-muted">-</span>
                                        {/if}
                                    </td>
                                    <td>
                                        {if $config.beta}
                                        <span class="am-badge am-badge-warning">是</span>
                                        {else/}
                                        <span class="am-badge am-badge-success">否</span>
                                        {/if}
                                    </td>
                                    <td>
                                        {if $config.is_active}
                                        <span class="am-badge am-badge-success">启用</span>
                                        {else/}
                                        <span class="am-badge am-badge-danger">禁用</span>
                                        {/if}
                                    </td>
                                    <td>
                                        <span class="am-badge am-badge-primary">{$config.group_count}</span>
                                    </td>
                                    <td>{$config.created_at}</td>
                                    <td>
                                        <div class="am-btn-group am-btn-group-xs">
                                            <a href="/manage/admin/update-config/edit?id={$config.id}"
                                               class="am-btn am-btn-default" title="编辑">
                                                <i class="am-icon-edit"></i>
                                            </a>
                                            <a href="/manage/admin/update-config/groups?name={$config.name}"
                                               class="am-btn am-btn-secondary" title="群组管理">
                                                <i class="am-icon-users"></i>
                                            </a>
                                            <button type="button" class="am-btn am-btn-danger"
                                                    onclick="deleteConfig({$config.id})" title="删除">
                                                <i class="am-icon-trash"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                                {/volist}
                            </tbody>
                        </table>
                    </div>
                    {else/}
                    <div class="am-text-center" style="padding: 60px 0;">
                        <i class="am-icon-inbox am-icon-lg am-text-muted"></i>
                        <h4 class="am-text-muted">暂无更新配置</h4>
                        <p class="am-text-muted">点击上方"添加配置"按钮创建第一个更新配置</p>
                        <a href="/manage/admin/update-config/edit" class="am-btn am-btn-primary">
                            <i class="am-icon-plus"></i> 添加配置
                        </a>
                    </div>
                    {/if}
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 强制更新控制面板 -->
    <div class="am-panel am-panel-default">
        <div class="am-panel-hd">
            <h3 class="am-panel-title">
                <i class="am-icon-exclamation-triangle am-text-warning"></i> 强制更新控制
            </h3>
        </div>
        <div class="am-panel-bd">
            <div class="am-g">
                <div class="am-u-md-6">
                    <div class="am-form-group">
                        <label>应用名称:</label>
                        <input type="text" id="forceUpdateAppName" class="am-form-field" value="bns-helper" readonly>
                    </div>
                </div>
                <div class="am-u-md-6">
                    <div class="am-form-group">
                        <label>当前状态:</label>
                        <div>
                            <span id="forceUpdateStatus" class="am-badge am-badge-secondary">检查中...</span>
                        </div>
                    </div>
                </div>
            </div>
            <div class="am-g">
                <div class="am-u-md-12">
                    <div class="am-alert am-alert-secondary">
                        <i class="am-icon-info-circle"></i>
                        <strong>说明:</strong> 启用强制更新后，所有在线客户端将在下次心跳时收到更新通知并自动退出进行更新。
                    </div>
                </div>
            </div>
            <div class="am-g">
                <div class="am-u-md-12">
                    <button type="button" class="am-btn am-btn-danger am-btn-sm" onclick="setForceUpdate(true)">
                        <i class="am-icon-power-off"></i> 启用强制更新
                    </button>
                    <button type="button" class="am-btn am-btn-success am-btn-sm" onclick="setForceUpdate(false)">
                        <i class="am-icon-check"></i> 清除强制更新
                    </button>
                    <button type="button" class="am-btn am-btn-secondary am-btn-sm" onclick="checkForceUpdateStatus()">
                        <i class="am-icon-refresh"></i> 刷新状态
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// 删除配置
function deleteConfig(id) {
    layer.confirm('确定要删除这个配置吗？', {
        icon: 3,
        title: '确认删除'
    }, function(index) {
        $.ajax({
            url: '/manage/admin/update-config',
            method: 'POST',
            data: {
                action: 'delete',
                id: id
            },
            success: function(res) {
                if (res.code === 1) {
                    layer.msg(res.msg, {icon: 1});
                    setTimeout(() => {
                        location.reload();
                    }, 1000);
                } else {
                    layer.msg(res.msg, {icon: 2});
                }
            },
            error: function() {
                layer.msg('网络错误，请重试', {icon: 2});
            }
        });
        layer.close(index);
    });
}

// 设置强制更新
function setForceUpdate(force) {
    const appName = document.getElementById('forceUpdateAppName').value;
    const action = force ? '启用' : '清除';

    layer.confirm(`确定要${action}强制更新吗？`, {
        icon: 3,
        title: '确认操作'
    }, function(index) {
        $.ajax({
            url: '/manage/admin/update-config',
            method: 'POST',
            data: {
                action: 'setForceUpdate',
                app_name: appName,
                force: force
            },
            success: function(res) {
                if (res.code === 1) {
                    layer.msg(res.msg, {icon: 1});
                    checkForceUpdateStatus(); // 刷新状态
                } else {
                    layer.msg(res.msg, {icon: 2});
                }
            },
            error: function() {
                layer.msg('网络错误，请重试', {icon: 2});
            }
        });
        layer.close(index);
    });
}

// 检查强制更新状态
function checkForceUpdateStatus() {
    const appName = document.getElementById('forceUpdateAppName').value;

    $.ajax({
        url: '/manage/admin/update-config',
        method: 'POST',
        data: {
            action: 'getForceUpdateStatus',
            app_name: appName
        },
        success: function(res) {
            if (res.code === 1) {
                const status = res.data.force_update;
                const statusElement = document.getElementById('forceUpdateStatus');

                if (status) {
                    statusElement.className = 'am-badge am-badge-danger';
                    statusElement.textContent = '强制更新已启用';
                } else {
                    statusElement.className = 'am-badge am-badge-success';
                    statusElement.textContent = '正常状态';
                }
            } else {
                layer.msg(res.msg, {icon: 2});
            }
        },
        error: function() {
            layer.msg('获取状态失败', {icon: 2});
        }
    });
}

// 页面加载时检查状态
$(document).ready(function() {
    checkForceUpdateStatus();
});
</script>
