<?php

namespace app\manage\controller\manage;

use app\common\controller\AdminBaseController;
use think\facade\View;

/**
 * 下载管理控制器
 */
class DownloadManage extends AdminBaseController
{
    /**
     * CDkey下载管理页面
     */
    public function index()
    {
        $downloadDir = \think\facade\App::getRootPath() . 'public/downloads/cdkeys';
        $files = [];

        if (is_dir($downloadDir)) {
            $fileList = scandir($downloadDir);
            foreach ($fileList as $file) {
                if ($file != '.' && $file != '..' && pathinfo($file, PATHINFO_EXTENSION) == 'txt') {
                    $filePath = $downloadDir . '/' . $file;
                    $files[] = [
                        'name' => $file,
                        'size' => filesize($filePath),
                        'size_formatted' => $this->formatFileSize(filesize($filePath)),
                        'time' => date('Y-m-d H:i:s', filemtime($filePath)),
                        'url' => '/downloads/cdkeys/' . $file,
                        'can_delete' => true
                    ];
                }
            }

            // 按时间倒序排列
            usort($files, function($a, $b) {
                return strtotime($b['time']) - strtotime($a['time']);
            });
        }

        return View::fetch('admin/downloads', [
            'files' => $files,
            'total_files' => count($files),
            'total_size' => $this->getTotalSize($files)
        ]);
    }

    /**
     * 删除CDkey下载文件
     */
    public function delete()
    {
        if (!$this->request->isPost()) {
            return $this->error('请求方式错误');
        }

        $filename = $this->request->param('filename');
        if (empty($filename)) {
            return $this->error('文件名不能为空');
        }

        // 安全检查：只允许删除txt文件，且文件名不能包含路径分隔符
        if (pathinfo($filename, PATHINFO_EXTENSION) != 'txt' ||
            strpos($filename, '/') !== false ||
            strpos($filename, '\\') !== false) {
            return $this->error('非法文件名');
        }

        $downloadDir = \think\facade\App::getRootPath() . 'public/downloads/cdkeys';
        $filePath = $downloadDir . '/' . $filename;

        if (!file_exists($filePath)) {
            return $this->error('文件不存在');
        }

        try {
            if (unlink($filePath)) {
                // 记录管理员操作
                $this->logAdminAction('delete_download_file', "删除下载文件: {$filename}", [
                    'filename' => $filename,
                    'file_path' => $filePath
                ]);
                
                return $this->success([], '文件删除成功');
            } else {
                return $this->error('文件删除失败');
            }
        } catch (\Exception $e) {
            return $this->error('文件删除失败: ' . $e->getMessage());
        }
    }

    /**
     * 批量删除文件
     */
    public function batchDelete()
    {
        if (!$this->request->isPost()) {
            return $this->error('请求方式错误');
        }

        $filenames = $this->request->param('filenames');
        if (empty($filenames)) {
            return $this->error('请选择要删除的文件');
        }

        $filenamesArray = is_array($filenames) ? $filenames : explode(',', $filenames);
        $downloadDir = \think\facade\App::getRootPath() . 'public/downloads/cdkeys';
        
        $successCount = 0;
        $failedFiles = [];

        foreach ($filenamesArray as $filename) {
            // 安全检查
            if (pathinfo($filename, PATHINFO_EXTENSION) != 'txt' ||
                strpos($filename, '/') !== false ||
                strpos($filename, '\\') !== false) {
                $failedFiles[] = $filename . ' (非法文件名)';
                continue;
            }

            $filePath = $downloadDir . '/' . $filename;
            
            if (!file_exists($filePath)) {
                $failedFiles[] = $filename . ' (文件不存在)';
                continue;
            }

            try {
                if (unlink($filePath)) {
                    $successCount++;
                } else {
                    $failedFiles[] = $filename . ' (删除失败)';
                }
            } catch (\Exception $e) {
                $failedFiles[] = $filename . ' (' . $e->getMessage() . ')';
            }
        }

        // 记录管理员操作
        $this->logAdminAction('batch_delete_download_files', "批量删除下载文件", [
            'total_files' => count($filenamesArray),
            'success_count' => $successCount,
            'failed_count' => count($failedFiles),
            'failed_files' => $failedFiles
        ]);

        if ($successCount > 0) {
            $message = "成功删除 {$successCount} 个文件";
            if (!empty($failedFiles)) {
                $message .= "，失败 " . count($failedFiles) . " 个";
            }
            return $this->success([
                'success_count' => $successCount,
                'failed_count' => count($failedFiles),
                'failed_files' => $failedFiles
            ], $message);
        } else {
            return $this->error('所有文件删除失败', 0, [
                'failed_files' => $failedFiles
            ]);
        }
    }

    /**
     * 清理过期文件
     */
    public function cleanExpired()
    {
        if (!$this->request->isPost()) {
            return $this->error('请求方式错误');
        }

        $days = $this->request->param('days', 30); // 默认清理30天前的文件
        $days = max(1, intval($days)); // 至少保留1天

        $downloadDir = \think\facade\App::getRootPath() . 'public/downloads/cdkeys';
        $expireTime = time() - ($days * 24 * 3600);
        
        $deletedCount = 0;
        $failedFiles = [];

        if (is_dir($downloadDir)) {
            $fileList = scandir($downloadDir);
            foreach ($fileList as $file) {
                if ($file != '.' && $file != '..' && pathinfo($file, PATHINFO_EXTENSION) == 'txt') {
                    $filePath = $downloadDir . '/' . $file;
                    $fileTime = filemtime($filePath);
                    
                    if ($fileTime < $expireTime) {
                        try {
                            if (unlink($filePath)) {
                                $deletedCount++;
                            } else {
                                $failedFiles[] = $file;
                            }
                        } catch (\Exception $e) {
                            $failedFiles[] = $file . ' (' . $e->getMessage() . ')';
                        }
                    }
                }
            }
        }

        // 记录管理员操作
        $this->logAdminAction('clean_expired_download_files', "清理过期下载文件: {$days}天前", [
            'days' => $days,
            'deleted_count' => $deletedCount,
            'failed_count' => count($failedFiles),
            'failed_files' => $failedFiles
        ]);

        return $this->success([
            'deleted_count' => $deletedCount,
            'failed_count' => count($failedFiles),
            'failed_files' => $failedFiles
        ], "清理完成，删除了 {$deletedCount} 个过期文件");
    }

    /**
     * 获取文件详情
     */
    public function getFileInfo()
    {
        $filename = $this->request->param('filename');
        
        if (empty($filename)) {
            return $this->error('文件名不能为空');
        }

        // 安全检查
        if (pathinfo($filename, PATHINFO_EXTENSION) != 'txt' ||
            strpos($filename, '/') !== false ||
            strpos($filename, '\\') !== false) {
            return $this->error('非法文件名');
        }

        $downloadDir = \think\facade\App::getRootPath() . 'public/downloads/cdkeys';
        $filePath = $downloadDir . '/' . $filename;

        if (!file_exists($filePath)) {
            return $this->error('文件不存在');
        }

        try {
            $fileInfo = [
                'name' => $filename,
                'size' => filesize($filePath),
                'size_formatted' => $this->formatFileSize(filesize($filePath)),
                'created_time' => date('Y-m-d H:i:s', filectime($filePath)),
                'modified_time' => date('Y-m-d H:i:s', filemtime($filePath)),
                'url' => '/downloads/cdkeys/' . $filename,
                'line_count' => $this->getFileLineCount($filePath)
            ];

            return $this->success($fileInfo, '获取文件信息成功');
        } catch (\Exception $e) {
            return $this->error('获取文件信息失败: ' . $e->getMessage());
        }
    }

    /**
     * 格式化文件大小
     */
    private function formatFileSize($bytes)
    {
        if ($bytes >= 1073741824) {
            return number_format($bytes / 1073741824, 2) . ' GB';
        } elseif ($bytes >= 1048576) {
            return number_format($bytes / 1048576, 2) . ' MB';
        } elseif ($bytes >= 1024) {
            return number_format($bytes / 1024, 2) . ' KB';
        } else {
            return $bytes . ' B';
        }
    }

    /**
     * 获取总文件大小
     */
    private function getTotalSize($files)
    {
        $totalSize = 0;
        foreach ($files as $file) {
            $totalSize += $file['size'];
        }
        return $this->formatFileSize($totalSize);
    }

    /**
     * 获取文件行数
     */
    private function getFileLineCount($filePath)
    {
        try {
            $lineCount = 0;
            $handle = fopen($filePath, 'r');
            if ($handle) {
                while (($line = fgets($handle)) !== false) {
                    $lineCount++;
                }
                fclose($handle);
            }
            return $lineCount;
        } catch (\Exception $e) {
            return 0;
        }
    }
}
