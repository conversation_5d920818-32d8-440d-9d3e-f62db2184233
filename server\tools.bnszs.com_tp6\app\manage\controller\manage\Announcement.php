<?php
namespace app\manage\controller\manage;

use think\facade\View;
use app\common\controller\AdminBaseController;
use app\manage\model\Announcement as AnnouncementModel;
use app\manage\service\AnnouncementPushService;
use app\manage\model\UserAdmin;

/**
 * 公告管理控制器
 * 需要管理员权限
 */
class Announcement extends AdminBaseController
{

    /**
     * 公告列表页面
     */
    public function index() {
        if($this->request->isPost()) {
            // 处理POST请求
            $action = $this->request->param('action', '');

            switch($action) {
                case 'delete':
                    return $this->deleteAnnouncement();
                case 'publish':
                    return $this->publishAnnouncement();
                case 'offline':
                    return $this->offlineAnnouncement();
                case 'batch':
                    return $this->batchOperation();
                case 'detail':
                    return $this->getDetail();
                case 'list':
                    return $this->getList();
                case 'checkUpdate':
                    return $this->checkUpdate();
                case 'stats':
                    return $this->getStats();
                case 'health':
                    return $this->health();
                default:
                    return json(['code' => 0, 'msg' => '未知操作']);
            }
        }

        $page = $this->request->param('page', 1);
        $filters = [
            'status' => $this->request->param('status', ''),
            'type' => $this->request->param('type', ''),
            'keyword' => $this->request->param('keyword', '')
        ];

        $announcements = AnnouncementModel::getAdminList($page, 20, $filters);
        $stats = AnnouncementModel::getStats();

        return View::fetch('announcement/index', [
            'announcements' => $announcements,
            'stats' => $stats,
            'filters' => $filters
        ]);
    }

    /**
     * 添加公告页面
     */
    public function add() {
        if ($this->request->isPost()) {
            try {
                $data = $this->request->post();
                
                // 验证数据
                $this->validateAnnouncementData($data);
                
                // 创建公告
                $announcement = AnnouncementModel::createAnnouncement($data, $this->getAdminId());

                if ($announcement && is_object($announcement)) {
                    // 如果创建的公告是已发布状态，发送实时推送通知
                    if (isset($data['status']) && $data['status'] == 1) {
                        AnnouncementPushService::publishAnnouncementUpdate($announcement->toArray());
                    }

                    return json(['code' => 1, 'msg' => '公告创建成功', 'url' => '/manage/admin/announcement']);
                } else {
                    return json(['code' => 0, 'msg' => '公告创建失败']);
                }

            } catch (\Exception $e) {
                return json(['code' => 0, 'msg' => '创建失败：' . $e->getMessage()]);
            }
        }
        
        return View::fetch('announcement/add');
    }

    /**
     * 编辑公告页面
     */
    public function edit() {
        $id = $this->request->param('id');
        
        if (!$id) {
            return json(['code' => 0, 'msg' => '参数错误']);
        }

        $announcement = AnnouncementModel::find($id);
        if (!$announcement) {
            return json(['code' => 0, 'msg' => '公告不存在']);
        }
        
        if ($this->request->isPost()) {
            try {
                $data = $this->request->post();
                
                // 验证数据
                $this->validateAnnouncementData($data);
                
                // 更新公告
                $result = $announcement->updateAnnouncement($data, $this->getAdminId());

                if ($result && is_object($result)) {
                    // 如果更新的公告是已发布状态，发送实时推送通知
                    if (isset($data['status']) && $data['status'] == 1) {
                        AnnouncementPushService::publishAnnouncementUpdate($result->toArray());
                    }

                    return json(['code' => 1, 'msg' => '公告更新成功', 'url' => '/manage/admin/announcement']);
                } else {
                    return json(['code' => 0, 'msg' => '公告更新失败']);
                }

            } catch (\Exception $e) {
                return json(['code' => 0, 'msg' => '更新失败：' . $e->getMessage()]);
            }
        }
        
        return View::fetch('announcement/edit', [
            'announcement' => $announcement
        ]);
    }

    /**
     * 删除公告（私有方法）
     */
    private function deleteAnnouncement() {
        $id = $this->request->param('id');

        if (!$id) {
            return json(['code' => 0, 'msg' => '参数错误']);
        }

        $announcement = AnnouncementModel::find($id);
        if (!$announcement) {
            return json(['code' => 0, 'msg' => '公告不存在']);
        }

        try {
            $result = $announcement->deleteAnnouncement();

            if ($result) {
                return json(['code' => 1, 'msg' => '公告删除成功']);
            } else {
                return json(['code' => 0, 'msg' => '公告删除失败']);
            }

        } catch (\Exception $e) {
            return json(['code' => 0, 'msg' => '删除失败：' . $e->getMessage()]);
        }
    }

    /**
     * 发布公告（私有方法）
     */
    private function publishAnnouncement() {
        $id = $this->request->param('id');

        if (!$id) {
            return json(['code' => 0, 'msg' => '参数错误']);
        }

        $announcement = AnnouncementModel::find($id);
        if (!$announcement) {
            return json(['code' => 0, 'msg' => '公告不存在']);
        }

        try {
            $result = $announcement->publish();

            if ($result) {
                // 发布成功后，发送实时推送通知
                AnnouncementPushService::publishAnnouncementUpdate($announcement->toArray());

                return json(['code' => 1, 'msg' => '公告发布成功']);
            } else {
                return json(['code' => 0, 'msg' => '公告发布失败']);
            }

        } catch (\Exception $e) {
            return json(['code' => 0, 'msg' => '发布失败：' . $e->getMessage()]);
        }
    }

    /**
     * 下线公告（私有方法）
     */
    private function offlineAnnouncement() {
        $id = $this->request->param('id');

        if (!$id) {
            return json(['code' => 0, 'msg' => '参数错误']);
        }

        $announcement = AnnouncementModel::find($id);
        if (!$announcement) {
            return json(['code' => 0, 'msg' => '公告不存在']);
        }

        try {
            $result = $announcement->offline();

            if ($result) {
                return json(['code' => 1, 'msg' => '公告已下线']);
            } else {
                return json(['code' => 0, 'msg' => '下线失败']);
            }

        } catch (\Exception $e) {
            return json(['code' => 0, 'msg' => '下线失败：' . $e->getMessage()]);
        }
    }

    /**
     * 批量操作（私有方法）
     */
    private function batchOperation() {
        $batchAction = $this->request->param('batch_action');
        $ids = $this->request->param('ids');

        if (!$batchAction || !$ids) {
            return json(['code' => 0, 'msg' => '参数错误']);
        }
        
        $ids = is_array($ids) ? $ids : explode(',', $ids);
        
        try {
            $count = 0;
            foreach ($ids as $id) {
                $announcement = AnnouncementModel::find($id);
                if ($announcement) {
                    switch ($batchAction) {
                        case 'publish':
                            $announcement->publish();
                            $count++;
                            break;
                        case 'offline':
                            $announcement->offline();
                            $count++;
                            break;
                        case 'delete':
                            $announcement->deleteAnnouncement();
                            $count++;
                            break;
                    }
                }
            }
            
            return json(['code' => 1, 'msg' => "成功处理 {$count} 条公告"]);

        } catch (\Exception $e) {
            return json(['code' => 0, 'msg' => '批量操作失败：' . $e->getMessage()]);
        }
    }

    /**
     * 获取公告详情（私有方法）
     */
    private function getDetail() {
        $id = $this->request->param('id');
        
        if (!$id) {
            return json(['code' => 1, 'msg' => '参数错误']);
        }
        
        $announcement = AnnouncementModel::find($id);
        if (!$announcement) {
            return json(['code' => 1, 'msg' => '公告不存在']);
        }
        
        return json([
            'code' => 0,
            'data' => $announcement->toArray()
        ]);
    }

    /**
     * 获取公告列表（API方法）
     */
    private function getList() {
        try {
            $clientType = $this->request->param('client_type', 'all');
            $version = $this->request->param('version', '');
            $limit = $this->request->param('limit', 20);

            $announcements = AnnouncementModel::getClientList($clientType, $version);

            // 限制返回数量
            if ($limit > 0 && count($announcements) > $limit) {
                $announcements = array_slice($announcements->toArray(), 0, $limit);
            }

            return json([
                'code' => 1,
                'msg' => '获取成功',
                'data' => [
                    'announcements' => $announcements,
                    'count' => count($announcements),
                    'version' => AnnouncementModel::getCurrentVersion()
                ]
            ]);

        } catch (\Exception $e) {
            return json(['code' => 0, 'msg' => '获取公告列表失败: ' . $e->getMessage()]);
        }
    }

    /**
     * 检查公告更新（API方法）
     */
    private function checkUpdate() {
        try {
            $clientVersion = $this->request->param('version', 0);
            $currentVersion = AnnouncementModel::getCurrentVersion();

            $hasUpdate = $currentVersion > $clientVersion;

            return json([
                'code' => 1,
                'msg' => '检查完成',
                'data' => [
                    'has_update' => $hasUpdate,
                    'current_version' => $currentVersion,
                    'client_version' => $clientVersion
                ]
            ]);

        } catch (\Exception $e) {
            return json(['code' => 0, 'msg' => '检查更新失败: ' . $e->getMessage()]);
        }
    }

    /**
     * 获取公告统计信息（API方法）
     */
    private function getStats() {
        try {
            $stats = AnnouncementModel::getStats();
            $currentVersion = AnnouncementModel::getCurrentVersion();

            return json([
                'code' => 1,
                'msg' => '获取成功',
                'data' => [
                    'stats' => $stats,
                    'version' => $currentVersion,
                    'timestamp' => time()
                ]
            ]);

        } catch (\Exception $e) {
            return json(['code' => 0, 'msg' => '获取统计信息失败: ' . $e->getMessage()]);
        }
    }

    /**
     * 健康检查（API方法）
     */
    private function health() {
        try {
            // 检查数据库连接
            $count = AnnouncementModel::count();
            $version = AnnouncementModel::getCurrentVersion();

            return json([
                'code' => 1,
                'msg' => '服务正常',
                'data' => [
                    'status' => 'healthy',
                    'announcement_count' => $count,
                    'current_version' => $version,
                    'timestamp' => time()
                ]
            ]);

        } catch (\Exception $e) {
            return json(['code' => 0, 'msg' => '服务异常: ' . $e->getMessage()]);
        }
    }

    /**
     * 获取公告详情（API方法）
     */
    public function detail() {
        try {
            $id = $this->request->param('id');

            if (!$id) {
                return json(['code' => 0, 'msg' => '参数错误']);
            }

            $announcement = AnnouncementModel::find($id);
            if (!$announcement) {
                return json(['code' => 0, 'msg' => '公告不存在']);
            }

            // 检查公告是否已发布且在有效期内
            if ($announcement->status !== AnnouncementModel::STATUS_PUBLISHED) {
                return json(['code' => 0, 'msg' => '公告未发布']);
            }

            // 检查时间范围
            $now = date('Y-m-d H:i:s');
            if ($announcement->start_time && $announcement->start_time > $now) {
                return json(['code' => 0, 'msg' => '公告未到显示时间']);
            }
            if ($announcement->end_time && $announcement->end_time < $now) {
                return json(['code' => 0, 'msg' => '公告已过期']);
            }

            // 增加查看次数
            $announcement->incrementViewCount();

            return json([
                'code' => 1,
                'msg' => '获取成功',
                'data' => [
                    'announcement' => $announcement->toArray()
                ]
            ]);

        } catch (\Exception $e) {
            return json(['code' => 0, 'msg' => '获取公告详情失败: ' . $e->getMessage()]);
        }
    }

    /**
     * 验证公告数据
     */
    private function validateAnnouncementData($data) {
        if (empty($data['title'])) {
            throw new \Exception('公告标题不能为空');
        }

        if (empty($data['content'])) {
            throw new \Exception('公告内容不能为空');
        }

        if (strlen($data['title']) > 200) {
            throw new \Exception('公告标题不能超过200个字符');
        }

        if (!in_array($data['type'], [1, 2, 3])) {
            throw new \Exception('公告类型无效');
        }

        if (!in_array($data['status'], [0, 1, 2])) {
            throw new \Exception('公告状态无效');
        }

        if (!in_array($data['target_client'], ['all', 'desktop', 'mobile'])) {
            throw new \Exception('目标客户端类型无效');
        }

        // 验证时间格式
        if (!empty($data['start_time']) && !strtotime($data['start_time'])) {
            throw new \Exception('开始时间格式无效');
        }

        if (!empty($data['end_time']) && !strtotime($data['end_time'])) {
            throw new \Exception('结束时间格式无效');
        }

        // 验证时间逻辑
        if (!empty($data['start_time']) && !empty($data['end_time'])) {
            if (strtotime($data['start_time']) >= strtotime($data['end_time'])) {
                throw new \Exception('结束时间必须大于开始时间');
            }
        }
    }
}
