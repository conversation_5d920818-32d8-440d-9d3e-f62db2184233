<?php

namespace app\manage\controller\manage;

use app\common\controller\AdminBaseController;
use app\manage\model\User;
use think\facade\View;
use think\facade\Cache;
use think\facade\Db;
use think\facade\Config;
use think\Exception;

/**
 * 用户管理控制器
 */
class UserManage extends AdminBaseController
{
    /**
     * 用户管理页面
     */
    public function index()
    {
        if($this->request->isPost()) {
            try {
                $mode = $this->request->param('mode');

                switch ($mode) {
                    case "register":
                        $uin = $this->request->param('uin');
                        User::AddWeb($uin, NULL, $this->getAdminId());
                        $this->logAdminAction('user_register', "注册用户: {$uin}");
                        break;

                    case "device":
                        $uid = intval($this->request->param('uid'));
                        if ($uid) {
                            $this->clearUserDeviceBinding($uid);
                            $this->logAdminAction('clear_device', "清除用户设备绑定: {$uid}");
                        }
                        break;

                    case "updateStatus":
                        $this->updateUserStatus();
                        break;

                    case "updatePermission":
                        $this->updateUserPermission();
                        break;

                    case "batchUpdateStatus":
                        $this->batchUpdateUserStatus();
                        break;

                    case "clearCache":
                        $this->clearUserCache();
                        break;

                    case "cdkey":
                        $this->bindCdkey();
                        break;

                    default:
                        return $this->error('未知操作');
                }

                return $this->success([], '操作成功');
            }
            catch(\Exception $e) {
                return $this->error($e->getMessage());
            }
        }

        // 筛选条件
        $search = $this->request->get('search');
        $status = $this->request->get('status');
        $permission = $this->request->get('permission');

        $data = User::WHERE(function($query) use ($search, $status, $permission) {
            if($search) {
                $query->where('uin', $search)
                      ->whereOr('uid', $search)
                      ->whereOr('name', 'like', "%{$search}%");
            }
            if(isset($status) && $status !== '') {
                $query->where('status', $status);
            }
            if(isset($permission) && $permission !== '') {
                $query->where('permission', $permission);
            }
        })->order('uid', 'desc')->paginate(20);

        // 获取统计信息
        $stats = [
            'total' => User::count(),
            'normal' => User::where('status', 0)->count(),
            'banned' => User::where('status', '>', 0)->count(),
            'premium' => User::where('permission', '>', 0)->count(),
        ];

        return View::fetch('admin/users', [
            'data' => $data,
            'stats' => $stats,
            'current_search' => $search,
            'current_status' => $status,
            'current_permission' => $permission
        ]);
    }

    /**
     * 更新用户状态
     */
    private function updateUserStatus()
    {
        $uid = intval($this->request->param('uid'));
        $status = intval($this->request->param('status'));
        $reason = $this->request->param('reason', '');

        if ($uid && isset($status)) {
            $user = User::where('uid', $uid)->find();
            if (!$user) {
                throw new Exception('用户不存在');
            }

            $user->status = $status;
            $user->save();

            // 记录操作日志
            User::AddLog($uid, 'status_change', "管理员修改状态: {$status}, 原因: {$reason}", $this->getAdminId());
            $this->logAdminAction('update_user_status', "修改用户状态: UID={$uid}, Status={$status}", ['uid' => $uid, 'status' => $status, 'reason' => $reason]);
        }
    }

    /**
     * 更新用户权限
     */
    private function updateUserPermission()
    {
        $uid = intval($this->request->param('uid'));
        $permission = intval($this->request->param('permission'));
        $reason = $this->request->param('reason', '');

        if ($uid && isset($permission)) {
            $user = User::where('uid', $uid)->find();
            if (!$user) {
                throw new Exception('用户不存在');
            }

            $user->permission = $permission;
            $user->save();

            // 记录操作日志
            User::AddLog($uid, 'permission_change', "管理员修改权限: {$permission}, 原因: {$reason}", $this->getAdminId());
            $this->logAdminAction('update_user_permission', "修改用户权限: UID={$uid}, Permission={$permission}", ['uid' => $uid, 'permission' => $permission, 'reason' => $reason]);
        }
    }

    /**
     * 批量更新用户状态
     */
    private function batchUpdateUserStatus()
    {
        $uids = $this->request->param('uids');
        $status = intval($this->request->param('status'));
        $reason = $this->request->param('reason', '');

        if ($uids && isset($status)) {
            $uidArray = is_array($uids) ? $uids : explode(',', $uids);
            foreach ($uidArray as $uid) {
                $uid = intval($uid);
                if ($uid) {
                    $user = User::where('uid', $uid)->find();
                    if ($user) {
                        $user->status = $status;
                        $user->save();

                        // 记录操作日志
                        User::AddLog($uid, 'batch_status_change', "管理员批量修改状态: {$status}, 原因: {$reason}", $this->getAdminId());
                    }
                }
            }
            $this->logAdminAction('batch_update_user_status', "批量修改用户状态: Status={$status}", ['uids' => $uidArray, 'status' => $status, 'reason' => $reason]);
        }
    }

    /**
     * 清除用户缓存
     */
    private function clearUserCache()
    {
        $uid = intval($this->request->param('uid'));
        if ($uid) {
            try {
                $redis = Cache::store('redis')->handler();
                // 清除用户相关的所有缓存
                $patterns = [
                    "user_expir_*",
                    "user_draw_{$uid}_*",
                    "device_signin_*",
                    "signin_unavailable_{$uid}_*"
                ];

                $clearedCount = 0;
                foreach ($patterns as $pattern) {
                    $keys = $redis->keys($pattern);
                    if (is_array($keys) && !empty($keys)) {
                        foreach ($keys as $key) {
                            if ($redis->del($key)) {
                                $clearedCount++;
                            }
                        }
                    }
                }

                // 清除用户设备绑定
                if ($redis->hDel("user_device_time", $uid)) {
                    $clearedCount++;
                }

                // 记录操作日志
                User::AddLog($uid, 'clear_cache', "管理员清除用户缓存，清理了{$clearedCount}个缓存项", $this->getAdminId());
                $this->logAdminAction('clear_user_cache', "清除用户缓存: UID={$uid}", ['uid' => $uid, 'cleared_count' => $clearedCount]);
            } catch (Exception $e) {
                throw new Exception("清除缓存失败: " . $e->getMessage());
            }
        }
    }

    /**
     * 清除用户设备绑定
     */
    private function clearUserDeviceBinding($uid)
    {
        // TP5.1兼容的Redis连接方式
        $redis = new \Redis();
        $config = \think\facade\Config::get('cache');
        $redis->connect($config['host'], $config['port']);
        if (!empty($config['password'])) {
            $redis->auth($config['password']);
        }
        $redis->select($config['select']);
        $redis->hDel("user_device_time", $uid);
        $redis->close();
    }

    /**
     * 绑定CDKEY
     */
    private function bindCdkey()
    {
        $uid = intval($this->request->param('uid'));
        $cdkey = $this->request->param('data');

        if (!$uid || !$cdkey) {
            throw new Exception('参数错误');
        }

        // 检查用户是否存在
        $user = User::where('uid', $uid)->find();
        if (!$user) {
            throw new Exception('用户不存在');
        }

        // 这里需要根据实际的CDKEY系统来实现绑定逻辑
        // 假设有一个CDKey模型来处理CDKEY相关操作
        try {
            // 检查CDKEY是否有效
            $cdkeyModel = Db::name('cdkey')->where('code', $cdkey)->find();
            if (!$cdkeyModel) {
                throw new Exception('CDKEY不存在');
            }

            if ($cdkeyModel['status'] != 0) {
                throw new Exception('CDKEY已被使用');
            }

            // 绑定CDKEY到用户
            Db::startTrans();
            try {
                // 更新CDKEY状态
                Db::name('cdkey')->where('code', $cdkey)->update([
                    'status' => 1,
                    'uid' => $uid,
                    'used_time' => date('Y-m-d H:i:s')
                ]);

                // 根据CDKEY类型给用户添加权限或其他奖励
                if ($cdkeyModel['type'] == 'vip') {
                    // 添加VIP权限
                    $user->permission = max($user->permission, 1);
                    $user->save();
                }

                // 记录操作日志
                User::AddLog($uid, 'cdkey_bind', "管理员绑定CDKEY: {$cdkey}", $this->getAdminId());
                $this->logAdminAction('bind_cdkey', "绑定CDKEY: UID={$uid}, CDKEY={$cdkey}", ['uid' => $uid, 'cdkey' => $cdkey]);

                Db::commit();
            } catch (\Exception $e) {
                Db::rollback();
                throw $e;
            }

        } catch (\Exception $e) {
            throw new Exception('绑定失败: ' . $e->getMessage());
        }
    }
}
