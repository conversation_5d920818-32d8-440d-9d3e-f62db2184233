<?php

namespace app\manage\controller\api;

use app\common\controller\AdminBaseController;
use app\manage\model\UpdateConfig as UpdateConfigModel;
use app\manage\model\UpdateGroup as UpdateGroupModel;
use think\Request;
use think\facade\Validate;
use think\facade\Cache;
use app\common\service\RedisService;

/**
 * 更新配置API控制器
 * 需要管理员权限
 */
class UpdateConfig extends AdminBaseController
{
    /**
     * 统一API入口
     */
    public function index()
    {
        $action = $this->request->param('action', '');

        switch($action) {
            case 'list':
                return $this->getList();
            case 'detail':
                return $this->getDetail();
            case 'save':
                return $this->saveConfig();
            case 'delete':
                return $this->deleteConfig();
            case 'toggle':
                return $this->toggleConfig();
            case 'refreshCache':
                return $this->refreshCache();
            default:
                return $this->error('未知操作');
        }
    }

    /**
     * 获取更新配置列表（私有方法）
     */
    private function getList()
    {
        try {
            $configs = UpdateConfigModel::getAll();

            // 为每个配置获取群组信息
            foreach ($configs as &$config) {
                $groups = UpdateGroupModel::getByAppName($config['name']);
                $config['groups'] = $groups;
                $config['group_count'] = count($groups);
            }

            return $this->success($configs, '获取成功');
        } catch (\Exception $e) {
            return $this->error('获取失败: ' . $e->getMessage());
        }
    }

    /**
     * 获取单个更新配置（私有方法）
     */
    private function getDetail()
    {
        $id = $this->request->param('id');

        if (!$id) {
            return $this->error('参数错误');
        }

        try {
            $config = UpdateConfigModel::find($id);
            if (!$config) {
                return $this->error('配置不存在');
            }

            // 获取群组信息
            $groups = UpdateGroupModel::getByAppName($config['name']);
            $config['groups'] = $groups;

            return $this->success($config, '获取成功');
        } catch (\Exception $e) {
            return $this->error('获取失败: ' . $e->getMessage());
        }
    }

    /**
     * 创建或更新配置（私有方法）
     */
    private function saveConfig()
    {
        // 使用基类的参数验证方法
        $rules = [
            'name'        => 'require|max:50',
            'current_version' => 'require|max:20',
            'executable_path' => 'require|max:100',
            'download_url'    => 'require|max:500',
            'plugin_version'  => 'max:20',
            'plugin_url'      => 'max:500',
        ];

        $data = $this->validateParams($rules);
        if ($data === false) {
            return $this->error($this->getValidateError());
        }

        try {
            // 保存配置
            $config = UpdateConfigModel::createOrUpdate($data);

            // 处理群组数据
            if (isset($data['groups']) && is_array($data['groups'])) {
                $this->saveGroups($data['name'], $data['groups']);
            }

            // 清除缓存
            $this->clearCache($data['name']);

            // 记录管理员操作
            $this->logAdminAction('update_config_save', "保存更新配置: {$data['name']}", $data);

            return $this->success($config, '保存成功');
        } catch (\Exception $e) {
            return $this->error('保存失败: ' . $e->getMessage());
        }
    }

    /**
     * 删除配置
     */
    private function deleteConfig()
    {
        $id = $this->request->param('id');

        if (!$id) {
            return $this->error('参数错误');
        }

        try {
            $config = UpdateConfigModel::find($id);
            if (!$config) {
                return $this->error('配置不存在');
            }

            $appName = $config['name'];

            // 删除配置
            UpdateConfigModel::deleteConfig($id);

            // 删除相关群组
            UpdateGroupModel::deleteByApp($appName);

            // 清除缓存
            $this->clearCache($appName);

            // 记录管理员操作
            $this->logAdminAction('update_config_delete', "删除更新配置: {$appName}", ['id' => $id, 'name' => $appName]);

            return $this->success([], '删除成功');
        } catch (\Exception $e) {
            return $this->error('删除失败: ' . $e->getMessage());
        }
    }

    /**
     * 启用/禁用配置
     */
    private function toggleConfig()
    {
        $id = $this->request->param('id');

        if (!$id) {
            return $this->error('参数错误');
        }

        try {
            $config = UpdateConfigModel::find($id);
            if (!$config) {
                return $this->error('配置不存在');
            }

            $result = UpdateConfigModel::toggleActive($id);

            if ($result) {
                // 清除缓存
                $this->clearCache($config['name']);

                // 记录管理员操作
                $this->logAdminAction('update_config_toggle', "切换更新配置状态: {$config['name']}", ['id' => $id, 'name' => $config['name']]);

                return $this->success([], '操作成功');
            } else {
                return $this->error('操作失败');
            }
        } catch (\Exception $e) {
            return $this->error('操作失败: ' . $e->getMessage());
        }
    }

    /**
     * 刷新缓存
     */
    private function refreshCache()
    {
        $appName = $this->request->param('name');

        try {
            if ($appName) {
                // 刷新指定应用的缓存
                $this->clearCache($appName);
                $msg = "应用 {$appName} 缓存刷新成功";
                $this->logAdminAction('refresh_cache', "刷新应用缓存: {$appName}");
            } else {
                // 刷新所有应用的缓存
                $apps = UpdateConfigModel::getAppList();
                foreach ($apps as $app) {
                    $this->clearCache($app);
                }
                $msg = "所有应用缓存刷新成功";
                $this->logAdminAction('refresh_cache', "刷新所有应用缓存");
            }

            return $this->success([], $msg);
        } catch (\Exception $e) {
            return $this->error('缓存刷新失败: ' . $e->getMessage());
        }
    }

    /**
     * 刷新Groups缓存
     */
    public function refreshGroupsCache()
    {
        try {
            $groupsCacheKey = "active_groups";

            // 删除Groups缓存
            RedisService::delete($groupsCacheKey);
            Cache::delete($groupsCacheKey);

            return json(['code' => 1, 'msg' => 'Groups缓存刷新成功']);
        } catch (\Exception $e) {
            return json(['code' => 0, 'msg' => 'Groups缓存刷新失败: ' . $e->getMessage()]);
        }
    }

    /**
     * 设置强制更新标志
     */
    public function setForceUpdate()
    {
        try {
            $appName = $this->request->post('app_name', 'bns-helper');
            $force = $this->request->post('force', false);

            $cacheKey = "force_update:{$appName}";

            if ($force) {
                // 设置强制更新标志，缓存1小时
                RedisService::setex($cacheKey, 3600, true);
                $msg = "强制更新标志已设置，所有在线客户端将在下次心跳时收到更新通知";
            } else {
                // 清除强制更新标志
                RedisService::delete($cacheKey);
                $msg = "强制更新标志已清除";
            }

            return json(['code' => 1, 'msg' => $msg]);
        } catch (\Exception $e) {
            return json(['code' => 0, 'msg' => '设置强制更新标志失败: ' . $e->getMessage()]);
        }
    }

    /**
     * 获取强制更新状态
     */
    public function getForceUpdateStatus()
    {
        try {
            $appName = $this->request->get('app_name', 'bns-helper');
            $beta = $this->request->get('beta', 0); // 0=正式版，1=测试版

            // 首先尝试从数据库获取
            $config = UpdateConfigModel::getByAppName($appName, $beta);

            if ($config) {
                return json([
                    'code' => 1,
                    'data' => [
                        'app_name' => $config->name,
                        'beta' => $config->beta,
                        'force_update' => $config->force_update,
                        'version' => $config->version,
                        'source' => 'database'
                    ]
                ]);
            }

            // 如果数据库没有，尝试从Redis缓存获取（兼容旧版本）
            $cacheKey = "force_update:{$appName}";
            $forceUpdate = RedisService::get($cacheKey);

            return json([
                'code' => 1,
                'data' => [
                    'app_name' => $appName,
                    'beta' => $beta,
                    'force_update' => !empty($forceUpdate),
                    'source' => 'cache',
                    'cache_key' => $cacheKey
                ]
            ]);
        } catch (\Exception $e) {
            return json(['code' => 0, 'msg' => '获取强制更新状态失败: ' . $e->getMessage()]);
        }
    }

    /**
     * 保存群组数据
     */
    private function saveGroups($appName, $groups)
    {
        // 删除现有群组
        UpdateGroupModel::deleteByApp($appName);
        
        // 保存新群组
        foreach ($groups as $groupType => $groupIds) {
            if (is_array($groupIds) && !empty($groupIds)) {
                UpdateGroupModel::batchCreate($appName, $groupType, $groupIds);
            }
        }
    }

    /**
     * 清除缓存
     */
    private function clearCache($appName)
    {
        $cacheKey = "update_config:{$appName}";
        $groupsCacheKey = "active_groups";

        // 删除Redis缓存（Go服务端使用的缓存）
        try {
            RedisService::delete($cacheKey);
            // 同时清除Groups缓存
            RedisService::delete($groupsCacheKey);
        } catch (\Exception $e) {
            // 记录错误但不影响主流程
            trace('Failed to clear Redis cache: ' . $e->getMessage(), 'error');
        }

        // 删除PHP本地缓存
        Cache::delete($cacheKey);
        Cache::delete($groupsCacheKey);
    }
}
