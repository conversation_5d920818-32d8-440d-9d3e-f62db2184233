<?php

namespace app\manage\controller\manage;

use app\common\controller\AdminBaseController;
use app\manage\model\CDkey;
use think\facade\View;
use think\facade\Db;
use think\Exception;

/**
 * CDkey管理控制器
 */
class CdkeyManage extends AdminBaseController
{
    /**
     * CDkey管理页面
     */
    public function index()
    {
        // 创建日志目录
        $logBaseDir = \think\facade\App::getRuntimePath() . 'log/cdkey_logs';
        $year = date('Y');
        $month = date('m');
        $logDir = $logBaseDir . '/' . $year . '/' . $month;

        // 确保目录存在并可写
        if (!is_dir($logDir)) {
            mkdir($logDir, 0755, true);
        }

        // 日志文件名
        $logFile = $logDir . '/' . date('Y-m-d') . '.log';

        // CDkey记录文件目录
        $cdkeyRecordDir = $logBaseDir . '/cdkey_records/' . $year . '/' . $month;
        if (!is_dir($cdkeyRecordDir)) {
            mkdir($cdkeyRecordDir, 0755, true);
        }

        try {
            if($_POST) {
                return $this->generateCdkeys($logFile);
            }

            // 筛选条件
            $search = $this->request->get('search');
            $showAll = $this->request->get('all', 'false') === 'true';
            $mode = $this->request->get('mode', '0');

            // 构建查询条件
            $where = [];

            // 如果不是查看所有，只显示当前管理员的CDKey
            if (!$showAll) {
                $where[] = ['admin', '=', $this->getAdminId()];
            }

            // 根据模式筛选
            switch ($mode) {
                case '1': // 当前可用
                    $where[] = ['RemainNum', '>', 0];
                    break;
                case '2': // 当前不可用
                    $where[] = ['RemainNum', '<=', 0];
                    break;
                default: // 全部
                    break;
            }

            // 搜索条件
            if (!empty($search)) {
                $where[] = ['cdkey|reason|type', 'like', '%' . $search . '%'];
            }

            // 使用关联查询获取管理员用户名
            $data = Db::name('bns_cdkey')
                ->alias('c')
                ->leftJoin('bns_useradmin a', 'c.admin = a.uid')
                ->field('c.*, a.username')
                ->where($where)
                ->order('c.id', 'desc')
                ->paginate(30);

            return View::fetch('admin/cdkey', [
                'data' => $data
            ]);

        } catch (\Exception $e) {
            // 记录未预期的错误
            $logContent = "[" . date('Y-m-d H:i:s') . "] =================== 未预期错误 ===================\n";
            $logContent .= "[" . date('Y-m-d H:i:s') . "] 错误: " . $e->getMessage() . "\n";
            $logContent .= "[" . date('Y-m-d H:i:s') . "] 文件: " . $e->getFile() . "\n";
            $logContent .= "[" . date('Y-m-d H:i:s') . "] 行号: " . $e->getLine() . "\n";
            $logContent .= "[" . date('Y-m-d H:i:s') . "] 堆栈跟踪: " . $e->getTraceAsString() . "\n";
            $logContent .= "[" . date('Y-m-d H:i:s') . "] =================== 错误结束 ===================\n\n";

            try {
                file_put_contents($logFile, $logContent, FILE_APPEND);
            } catch (\Exception $logException) {
                // 如果日志写入失败，直接返回错误信息
                return $this->error('系统错误: ' . $e->getMessage());
            }

            return $this->error('系统错误，请查看日志: ' . $e->getMessage());
        }
    }

    /**
     * 生成CDkey
     */
    private function generateCdkeys($logFile)
    {
        $logContent = "[" . date('Y-m-d H:i:s') . "] =================== 开始生成CDKey ===================\n";
        $logContent .= "[" . date('Y-m-d H:i:s') . "] 管理员ID: " . $this->getAdminId() . "\n";

        try {
            $usableTimes = $_POST['usableTimes'] ?? '';
            $changeTimes = $_POST['changeTimes'] ?? '';
            $startTime =  $_POST['startTime'] ?? '';
            $endTime = $_POST['endTime'] ?? '';
            $batch = $_POST['batch'] ?? null;
            $num = $_POST['num'] ?? '';
            $discern = $_POST['discern'] ?? '';
            $remark = $_POST['remark'] ?? '';
            $type = $_POST['type'] ?? '';
            $SpecifiedValue = $_POST['specified-value'] ?? '';

            // 记录参数
            $logContent .= "[" . date('Y-m-d H:i:s') . "] 参数信息:\n";
            $logContent .= "使用次数: {$usableTimes}\n";
            $logContent .= "修改次数: {$changeTimes}\n";
            $logContent .= "开始时间: {$startTime}\n";
            $logContent .= "结束时间: {$endTime}\n";
            $logContent .= "批次: {$batch}\n";
            $logContent .= "生成数量: {$num}\n";
            $logContent .= "识别码: {$discern}\n";
            $logContent .= "备注: {$remark}\n";
            $logContent .= "类型: {$type}\n";
            $logContent .= "指定值: {$SpecifiedValue}\n";

            // 处理时间数据
            if ($type == "customize" || $type == "client") {
                $timeType  = $_POST['time-limit-type'] ?? '';
                $duration  = $_POST['usable-duration'] ?? '';
                $fixedTime = $_POST['fixed-expiration-time'] ?? '';

                $logContent .= "时间类型: {$timeType}\n";
                $logContent .= "持续时间: {$duration}\n";
                $logContent .= "固定过期时间: {$fixedTime}\n";

                if(!strtotime($fixedTime)) $fixedTime = null;

                switch ($timeType) {
                    case 'fixed':
                        break;

                    case 'duration':
                        if ($duration <= 0) {
                            $logContent .= "[" . date('Y-m-d H:i:s') . "] 错误: 时间天数必须大于 0\n";
                            throw new Exception("时间天数必须大于 0");
                        }
                        break;

                    default:
                        $logContent .= "[" . date('Y-m-d H:i:s') . "] 错误: 无效的时间类型\n";
                        throw new Exception("无效的时间类型");
                }
            }

            // 批量生成时强制设置可使用次数为1
            $usableTimes = 1;

            //最大修改次数处理
            if (empty($changeTimes)) $changeTimes = 3;
            else if ($changeTimes == '∞') $changeTimes = 'null';
            else $changeTimes = min(9999, intval($changeTimes));

            // 批量生成时不使用批次信息，设为null
            $batch = null;

            if (empty($num)) $num = 1;
            else if ($num > 5000) $num = 5000;
            else $num = intval($num);

            $CDkeyMsg = '';
            $generatedCDkeys = []; // 存储生成的CDkey列表

            for ($n = 0; $n < $num; $n++) {
                $CDkey = CDkey::Random($discern, $n);
                if (empty($CDkey)) {
                    $logContent .= "[" . date('Y-m-d H:i:s') . "] 警告: 第{$n}个CDkey生成为空\n";
                    continue;
                }

                $logContent .= "[" . date('Y-m-d H:i:s') . "] 开始插入第{$n}个CDkey: {$CDkey}\n";

                Db::startTrans();
                try {
                    // 记录主表数据
                    $mainData = [
                        'cdkey'=>$CDkey,
                        'type'=>$type,
                        'MaxNum'=>$usableTimes,
                        'RemainNum'=>$usableTimes,
                        'reason'=>$remark,
                        'batch'=>$batch,
                        'admin'=>$this->getAdminId(),
                        'startTime'=> strtotime($startTime) ? $startTime : Date("Y-m-d H:i:s"),
                        'endTime'  => strtotime($endTime) ? $endTime : null,
                    ];
                    $logContent .= "[" . date('Y-m-d H:i:s') . "] 主表数据: " . json_encode($mainData, JSON_UNESCAPED_UNICODE) . "\n";

                    // 插入主表
                    $mainResult = Db::name('bns_cdkey')->insert($mainData);
                    $logContent .= "[" . date('Y-m-d H:i:s') . "] 主表插入" . ($mainResult ? '成功' : '失败') . "\n";

                    // 子表数据准备
                    switch($type) {
                        case "client":
                        case "customize":
                            $subData = [
                                'cdkey'=>$CDkey,
                                'timeType'=>$timeType,
                                'fixed'=>$fixedTime,
                                'duration'=>$duration,
                                'modifyNum'=>$changeTimes
                            ];
                            $logContent .= "[" . date('Y-m-d H:i:s') . "] customize子表数据: " . json_encode($subData, JSON_UNESCAPED_UNICODE) . "\n";
                            $subResult = Db::name('bns_cdkey_customize')->insert($subData);
                            break;

                        case "drawtimes":
                            $subData = [
                                'cdkey'=>$CDkey,
                                'schedule'=>null,
                                'max_times'=>$SpecifiedValue,
                                'cycleType'=>'none'
                            ];
                            $logContent .= "[" . date('Y-m-d H:i:s') . "] drawtimes子表数据: " . json_encode($subData, JSON_UNESCAPED_UNICODE) . "\n";
                            $subResult = Db::name('bns_cdkey_drawtimes')->insert($subData);
                            break;

                        default:
                            throw new Exception("缺少子表处理");
                    }

                    $logContent .= "[" . date('Y-m-d H:i:s') . "] 子表插入" . ($subResult ? '成功' : '失败') . "\n";

                    Db::commit();
                    $logContent .= "[" . date('Y-m-d H:i:s') . "] 事务提交成功\n";

                    $CDkeyMsg .= $CDkey . "\n";
                    $generatedCDkeys[] = $CDkey; // 添加到生成列表

                } catch (\Exception $e) {
                    Db::rollback();
                    $logContent .= "[" . date('Y-m-d H:i:s') . "] 数据库错误: " . $e->getMessage() . "\n";
                    $logContent .= "[" . date('Y-m-d H:i:s') . "] SQL: " . Db::getLastSql() . "\n";
                    $logContent .= "[" . date('Y-m-d H:i:s') . "] 堆栈: " . $e->getTraceAsString() . "\n";
                    throw $e;
                }
            }

            $logContent .= "[" . date('Y-m-d H:i:s') . "] CDKey生成成功，共生成{$num}个\n";
            $logContent .= "[" . date('Y-m-d H:i:s') . "] =================== 生成结束 ===================\n\n";
            file_put_contents($logFile, $logContent, FILE_APPEND);

            // 创建可下载的CDkey文件
            $downloadInfo = $this->createDownloadFile($generatedCDkeys, $discern, $type, $timeType ?? '', $duration ?? '', $fixedTime ?? '', $startTime, $endTime, $remark, $num);

            // 记录管理员操作
            $this->logAdminAction('generate_cdkeys', "生成CDkey: {$num}个", [
                'type' => $type,
                'discern' => $discern,
                'num' => $num,
                'remark' => $remark
            ]);

            return json([
                'code' => true,
                'msg' => "生成完毕，共生成" . $num . "个CDkey",
                'download_url' => $downloadInfo['url'],
                'filename' => $downloadInfo['filename'],
                'cdkeys' => $CDkeyMsg
            ]);

        } catch (\Exception $e) {
            $logContent .= "[" . date('Y-m-d H:i:s') . "] 错误: " . $e->getMessage() . "\n";
            $logContent .= "[" . date('Y-m-d H:i:s') . "] =================== 生成失败 ===================\n\n";
            file_put_contents($logFile, $logContent, FILE_APPEND);

            return $this->error($e->getMessage());
        }
    }

    /**
     * 创建下载文件
     */
    private function createDownloadFile($generatedCDkeys, $discern, $type, $timeType, $duration, $fixedTime, $startTime, $endTime, $remark, $num)
    {
        $downloadDir = \think\facade\App::getRootPath() . 'public/downloads/cdkeys';
        if (!is_dir($downloadDir)) {
            mkdir($downloadDir, 0755, true);
        }

        $timestamp = date('Y-m-d_H-i-s');
        $filename = "cdkeys_{$discern}_{$timestamp}.txt";
        $downloadFile = $downloadDir . '/' . $filename;

        // 创建文件内容
        $fileContent = "# CDkey批量生成记录\n";
        $fileContent .= "# 生成时间: " . date('Y-m-d H:i:s') . "\n";
        $fileContent .= "# 生成管理员: {$this->getAdminId()}\n";
        $fileContent .= "# 识别前缀: {$discern}\n";
        $fileContent .= "# 类型: {$type}\n";
        $fileContent .= "# 可使用次数: 1\n";
        $fileContent .= "# 期限类型: " . ($timeType == 'duration' ? '时长模式' : '固定时间模式') . "\n";
        if ($timeType == 'duration') {
            $fileContent .= "# 有效时长: {$duration}天\n";
        } else {
            $fileContent .= "# 固定到期时间: {$fixedTime}\n";
        }
        $fileContent .= "# 激活开始时间: {$startTime}\n";
        $fileContent .= "# 激活结束时间: {$endTime}\n";
        $fileContent .= "# 备注: {$remark}\n";
        $fileContent .= "# 总数量: {$num}\n";
        $fileContent .= "# ==========================================\n\n";

        foreach ($generatedCDkeys as $cdkey) {
            $fileContent .= $cdkey . "\n";
        }

        file_put_contents($downloadFile, $fileContent);

        return [
            'url' => '/downloads/cdkeys/' . $filename,
            'filename' => $filename
        ];
    }
}
