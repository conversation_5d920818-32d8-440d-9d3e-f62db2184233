{layout name="manage/template" /}

<div class="admin-content">
    <div class="admin-content-body">
        <div class="am-cf am-padding am-padding-bottom-0">
            <div class="am-fl am-cf">
                <strong class="am-text-primary am-text-lg">{if $isEdit}编辑更新配置{else/}添加更新配置{/if}</strong> /
                <small>Update Config {if $isEdit}Edit{else/}Add{/if}</small>
            </div>
        </div>

        <hr>

        <div class="am-g">
            <div class="am-u-sm-12">
                <div class="am-panel am-panel-default">
                    <div class="am-panel-hd">
                        <h3 class="am-panel-title">
                            <i class="am-icon-cog"></i> 配置信息
                            <div class="am-fr">
                                <a href="/manage/admin/update-config" class="am-btn am-btn-default am-btn-xs">
                                    <i class="am-icon-arrow-left"></i> 返回列表
                                </a>
                            </div>
                        </h3>
                    </div>

                    <div class="am-panel-bd">
                    <form method="post" id="configForm">
                        {if $isEdit && $config}
                        <input type="hidden" name="id" value="{$config.id}">
                        {/if}
                        
                        <div class="row">
                            <div class="col-md-6">
                                <!-- 基本信息 -->
                                <div class="mb-3">
                                    <label for="name" class="form-label">应用名称 <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control" id="name" name="name" 
                                           value="{if $config}{$config.name}{/if}" 
                                           required maxlength="50" placeholder="如：bns-helper">
                                    <div class="form-text">应用的唯一标识符</div>
                                </div>
                                
                                <div class="mb-3">
                                    <label for="version" class="form-label">当前版本 <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control" id="version" name="version" 
                                           value="{if $config}{$config.version}{/if}" 
                                           required maxlength="20" placeholder="如：1.0.0">
                                    <div class="form-text">当前最新版本号</div>
                                </div>
                                
                                <div class="mb-3">
                                    <label for="executable_path" class="form-label">可执行文件路径 <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control" id="executable_path" name="executable_path" 
                                           value="{if $config}{$config.executable_path}{/if}" 
                                           required maxlength="100" placeholder="如：剑灵小助手.exe">
                                    <div class="form-text">更新后的可执行文件名</div>
                                </div>
                                
                                <div class="mb-3">
                                    <label for="url" class="form-label">下载链接 <span class="text-danger">*</span></label>
                                    <input type="url" class="form-control" id="url" name="url" 
                                           value="{if $config}{$config.url}{/if}" 
                                           required maxlength="500" placeholder="https://example.com/download.zip">
                                    <div class="form-text">更新包的下载地址</div>
                                </div>
                                
                                <div class="mb-3">
                                    <label for="checksum" class="form-label">文件校验和</label>
                                    <input type="text" class="form-control" id="checksum" name="checksum" 
                                           value="{if $config}{$config.checksum}{/if}" 
                                           maxlength="64" placeholder="SHA256校验和（可选）">
                                    <div class="form-text">用于验证下载文件完整性的SHA256值</div>
                                </div>
                            </div>
                            
                            <div class="col-md-6">
                                <!-- 插件信息 -->
                                <div class="mb-3">
                                    <label for="plugin_version" class="form-label">插件版本</label>
                                    <input type="text" class="form-control" id="plugin_version" name="plugin_version" 
                                           value="{if $config}{$config.plugin_version}{/if}" 
                                           maxlength="20" placeholder="如：1.0.0">
                                    <div class="form-text">插件版本号（可选）</div>
                                </div>
                                
                                <div class="mb-3">
                                    <label for="plugin_url" class="form-label">插件下载链接</label>
                                    <input type="url" class="form-control" id="plugin_url" name="plugin_url" 
                                           value="{if $config}{$config.plugin_url}{/if}" 
                                           maxlength="500" placeholder="https://example.com/plugin.zip">
                                    <div class="form-text">插件的下载地址（可选）</div>
                                </div>
                                
                                <!-- 设置选项 -->
                                <div class="mb-3">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="beta" name="beta" value="1"
                                               {if $config && $config.beta}checked{/if}>
                                        <label class="form-check-label" for="beta">
                                            测试版本
                                        </label>
                                        <div class="form-text">标记为测试版本</div>
                                    </div>
                                </div>
                                
                                <div class="mb-3">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="is_active" name="is_active" value="1"
                                               {if !$config || $config.is_active}checked{/if}>
                                        <label class="form-check-label" for="is_active">
                                            启用配置
                                        </label>
                                        <div class="form-text">是否启用此更新配置</div>
                                    </div>
                                </div>
                                
                                <!-- 操作按钮 -->
                                <div class="d-grid gap-2 d-md-flex justify-content-md-end mt-4">
                                    <button type="button" class="btn btn-outline-secondary" onclick="history.back()">取消</button>
                                    <button type="submit" class="btn btn-primary">
                                        {if $isEdit}更新配置{else/}保存配置{/if}
                                    </button>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// 表单提交
document.getElementById('configForm').addEventListener('submit', function(e) {
    e.preventDefault();
    
    const formData = new FormData(this);
    
    // 验证必填字段
    const name = formData.get('name');
    const version = formData.get('version');
    const executablePath = formData.get('executable_path');
    const url = formData.get('url');
    
    if (!name.trim()) {
        layer.msg('请输入应用名称', {icon: 2});
        return;
    }
    
    if (!version.trim()) {
        layer.msg('请输入版本号', {icon: 2});
        return;
    }
    
    if (!executablePath.trim()) {
        layer.msg('请输入可执行文件路径', {icon: 2});
        return;
    }
    
    if (!url.trim()) {
        layer.msg('请输入下载链接', {icon: 2});
        return;
    }
    
    // 提交表单
    const submitBtn = this.querySelector('button[type="submit"]');
    submitBtn.disabled = true;
    submitBtn.textContent = '保存中...';
    
    // 添加action参数
    formData.append('action', 'save');

    $.ajax({
        url: '/manage/admin/update-config',
        method: 'POST',
        data: formData,
        processData: false,
        contentType: false,
        success: function(res) {
            if (res.code === 1) {
                layer.msg(res.msg, {icon: 1});
                setTimeout(() => {
                    if (res.url) {
                        window.location.href = res.url;
                    } else {
                        window.location.href = '/manage/admin/update-config';
                    }
                }, 1000);
            } else {
                layer.msg(res.msg, {icon: 2});
                submitBtn.disabled = false;
                submitBtn.textContent = '{if $isEdit}更新配置{else/}保存配置{/if}';
            }
        },
        error: function() {
            layer.msg('网络错误，请重试', {icon: 2});
            submitBtn.disabled = false;
            submitBtn.textContent = '{if $isEdit}更新配置{else/}保存配置{/if}';
        }
    });
});
</script>
