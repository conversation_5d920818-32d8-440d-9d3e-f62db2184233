<?php

namespace app\manage\controller\manage;

use app\common\controller\AdminBaseController;
use app\manage\model\Profile as ProfileModel;
use think\facade\View;

/**
 * 配置文件管理控制器
 */
class ProfileManage extends AdminBaseController
{
    /**
     * 配置文件管理页面
     */
    public function index()
    {
        // 筛选条件
        $mode = (integer)$this->request->get('mode', 0);
        $search = $this->request->get('search');
        
        $data = ProfileModel::WHERE(function($query) use ($mode, $search) {
            switch($mode) {
                case 1: 
                    $query->where('status', 0); 
                    break;
                case 2: 
                    $query->where('status', 2); 
                    break;
            }
            
            if (!empty($search)) {
                $query->where('name|description', 'like', "%{$search}%");
            }
        })->order('id', 'desc')->paginate(30);

        return View::fetch('admin/profiles', [
            'data' => $data,
            'current_mode' => $mode,
            'current_search' => $search
        ]);
    }

    /**
     * 配置文件详情
     */
    public function detail()
    {
        $id = $this->request->param('id');
        
        if (!$id) {
            return $this->error('参数错误');
        }
        
        $profile = ProfileModel::find($id);
        if (!$profile) {
            return $this->error('配置文件不存在');
        }
        
        return View::fetch('admin/profile_examine', [
            'profile' => $profile
        ]);
    }

    /**
     * 更新配置文件状态
     */
    public function updateStatus()
    {
        if (!$this->request->isPost()) {
            return $this->error('请求方式错误');
        }
        
        $id = $this->request->param('id');
        $status = $this->request->param('status');
        
        if (!$id || !isset($status)) {
            return $this->error('参数错误');
        }
        
        try {
            $profile = ProfileModel::find($id);
            if (!$profile) {
                return $this->error('配置文件不存在');
            }
            
            $oldStatus = $profile->status;
            $profile->status = $status;
            $profile->save();
            
            // 记录管理员操作
            $this->logAdminAction('update_profile_status', "修改配置文件状态: ID={$id}, Status={$oldStatus}->{$status}", [
                'id' => $id,
                'old_status' => $oldStatus,
                'new_status' => $status
            ]);
            
            return $this->success([], '状态更新成功');
        } catch (\Exception $e) {
            return $this->error('状态更新失败: ' . $e->getMessage());
        }
    }

    /**
     * 删除配置文件
     */
    public function delete()
    {
        if (!$this->request->isPost()) {
            return $this->error('请求方式错误');
        }
        
        $id = $this->request->param('id');
        
        if (!$id) {
            return $this->error('参数错误');
        }
        
        try {
            $profile = ProfileModel::find($id);
            if (!$profile) {
                return $this->error('配置文件不存在');
            }
            
            $profileName = $profile->name;
            $profile->delete();
            
            // 记录管理员操作
            $this->logAdminAction('delete_profile', "删除配置文件: ID={$id}, Name={$profileName}", [
                'id' => $id,
                'name' => $profileName
            ]);
            
            return $this->success([], '删除成功');
        } catch (\Exception $e) {
            return $this->error('删除失败: ' . $e->getMessage());
        }
    }

    /**
     * 批量操作
     */
    public function batchAction()
    {
        if (!$this->request->isPost()) {
            return $this->error('请求方式错误');
        }
        
        $ids = $this->request->param('ids');
        $action = $this->request->param('action');
        
        if (!$ids || !$action) {
            return $this->error('参数错误');
        }
        
        $idsArray = is_array($ids) ? $ids : explode(',', $ids);
        
        try {
            switch ($action) {
                case 'delete':
                    $this->batchDelete($idsArray);
                    break;
                case 'approve':
                    $this->batchUpdateStatus($idsArray, 0);
                    break;
                case 'reject':
                    $this->batchUpdateStatus($idsArray, 2);
                    break;
                default:
                    return $this->error('未知操作');
            }
            
            return $this->success([], '批量操作成功');
        } catch (\Exception $e) {
            return $this->error('批量操作失败: ' . $e->getMessage());
        }
    }

    /**
     * 批量删除
     */
    private function batchDelete($ids)
    {
        foreach ($ids as $id) {
            $profile = ProfileModel::find($id);
            if ($profile) {
                $profile->delete();
            }
        }
        
        // 记录管理员操作
        $this->logAdminAction('batch_delete_profiles', "批量删除配置文件", [
            'ids' => $ids,
            'count' => count($ids)
        ]);
    }

    /**
     * 批量更新状态
     */
    private function batchUpdateStatus($ids, $status)
    {
        foreach ($ids as $id) {
            $profile = ProfileModel::find($id);
            if ($profile) {
                $profile->status = $status;
                $profile->save();
            }
        }
        
        // 记录管理员操作
        $this->logAdminAction('batch_update_profile_status', "批量更新配置文件状态: Status={$status}", [
            'ids' => $ids,
            'status' => $status,
            'count' => count($ids)
        ]);
    }

    /**
     * 获取统计信息
     */
    public function getStats()
    {
        try {
            $stats = [
                'total' => ProfileModel::count(),
                'pending' => ProfileModel::where('status', 1)->count(),
                'approved' => ProfileModel::where('status', 0)->count(),
                'rejected' => ProfileModel::where('status', 2)->count(),
            ];
            
            return $this->success($stats, '获取统计信息成功');
        } catch (\Exception $e) {
            return $this->error('获取统计信息失败: ' . $e->getMessage());
        }
    }
}
