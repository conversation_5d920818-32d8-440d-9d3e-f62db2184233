<?php

namespace app\manage\controller\manage;

use app\common\controller\AdminBaseController;
use think\facade\Cache;
use think\facade\Db;
use think\facade\View;
use app\manage\model\UserAdmin as UserAdmin;

/**
 * 签到管理控制器
 * 需要管理员权限
 */
class SigninManage extends AdminBaseController
{
    /**
     * 获取管理员信息
     */
    private function getAdminInfo()
    {
        static $adminInfo = null;
        if ($adminInfo === null) {
            $adminInfo = UserAdmin::where('uid', $this->getAdminId())->find();
            if (!$adminInfo) {
                $adminInfo = [
                    'uid' => $this->getAdminId(),
                    'username' => 'admin',
                    'super' => 0,
                    'power' => ''
                ];
            }
        }
        return $adminInfo;
    }

    /**
     * 检查签到管理权限
     */
    private function checkSigninPermission() {
        $adminInfo = $this->getAdminInfo();

        // 超级管理员拥有所有权限
        if($adminInfo['super'] == 1) {
            return true;
        }

        // 检查是否有签到管理相关权限 (14, 15)
        $powers = explode(',', $adminInfo['power']);
        $signinPowers = [14, 15]; // 查看、管理权限
        
        foreach($signinPowers as $power) {
            if(in_array($power, $powers)) {
                return true;
            }
        }
        
        return false;
    }

    /**
     * 检查特定权限
     */
    private function checkPermission($powerID) {
        $adminInfo = $this->getAdminInfo();

        if($adminInfo['super'] == 1) {
            return true;
        }

        $powers = explode(',', $adminInfo['power']);
        return in_array($powerID, $powers);
    }

    /**
     * 签到管理页面
     */
    public function index() {
        if($this->request->isPost()) {
            // 处理POST请求
            $action = $this->request->param('action', '');
            
            switch($action) {
                case 'clearCache':
                    return $this->clearSigninCache();
                case 'getCacheStats':
                    return $this->getCacheStats();
                case 'testRedis':
                    return $this->testRedisConnection();
                default:
                    return json(['code' => 0, 'msg' => '未知操作']);
            }
        }
        
        try {
            // 获取Redis连接配置
            $redisConfig = config('cache');

            $data = [
                'admin_info' => $this->getAdminInfo(),
                'redis_config' => $redisConfig,
                'can_manage' => $this->checkPermission(15) // 管理权限
            ];

            return View::fetch('/admin/signin_manage', $data);

        } catch (\Exception $e) {
            abort(500, '获取签到管理页面失败: ' . $e->getMessage());
        }
    }

    /**
     * 清除签到缓存
     */
    private function clearSigninCache() {
        if(!$this->checkPermission(15)) {
            return json(['code' => 0, 'msg' => '您没有签到管理权限']);
        }

        try {
            $cacheType = $this->request->param('cache_type', 'all');
            $activityId = $this->request->param('activity_id', '');

            // 获取Redis连接
            $redis = Cache::store('redis')->handler();

            $clearedKeys = [];
            $patterns = [];

            switch ($cacheType) {
                case 'activity_rewards':
                    // 清除活动奖励缓存
                    if ($activityId) {
                        $patterns[] = "activity_rewards_{$activityId}";
                    } else {
                        $patterns[] = "activity_rewards_*";
                    }
                    break;

                case 'user_draw':
                    // 清除用户签到数据缓存
                    if ($activityId) {
                        $patterns[] = "user_draw_*_{$activityId}";
                    } else {
                        $patterns[] = "user_draw_*";
                    }
                    break;

                case 'device_signin':
                    // 清除设备签到状态缓存
                    if ($activityId) {
                        $patterns[] = "device_signin_{$activityId}_*";
                    } else {
                        $patterns[] = "device_signin_*";
                    }
                    break;

                case 'signin_unavailable':
                    // 清除签到不可用状态缓存
                    $patterns[] = "signin_unavailable_*";
                    break;

                case 'signin_time':
                    // 清除用户签到时间缓存（哈希表）
                    if ($activityId) {
                        $patterns[] = "signin_time_{$activityId}";
                    } else {
                        $patterns[] = "signin_time_*";
                    }
                    break;

                case 'all':
                default:
                    // 清除所有签到相关缓存
                    $patterns = [
                        'activity_rewards_*',
                        'user_draw_*',
                        'device_signin_*',
                        'signin_unavailable_*',
                        'signin_time_*'  // 新增：用户签到时间哈希表缓存
                    ];
                    break;
            }

            // 执行缓存清理
            foreach ($patterns as $pattern) {
                $keys = $redis->keys($pattern);
                if (is_array($keys) && !empty($keys)) {
                    foreach ($keys as $key) {
                        if ($redis->del($key)) {
                            $clearedKeys[] = $key;
                        }
                    }
                }
            }

            $redis->close();

            // 记录管理员操作日志
            $this->logAdminAction('clear_signin_cache', "清理签到缓存: {$cacheType}", [
                'cache_type' => $cacheType,
                'activity_id' => $activityId,
                'cleared_keys_count' => count($clearedKeys),
                'patterns' => $patterns
            ]);

            return json(['code' => 1, 'msg' => '缓存清理成功，共清理了 ' . count($clearedKeys) . ' 个缓存键']);

        } catch (\Exception $e) {
            return json(['code' => 0, 'msg' => '缓存清理失败: ' . $e->getMessage()]);
        }
    }

    /**
     * 测试Redis连接（私有方法）
     */
    private function testRedisConnection() {
        try {
            $redis = Cache::store('redis')->handler();

            // 测试基本连接
            $redis->ping();

            // 测试读写
            $testKey = 'test_connection_' . time();
            $redis->set($testKey, 'test_value', 10);
            $value = $redis->get($testKey);
            $redis->del($testKey);

            if ($value === 'test_value') {
                return json([
                    'code' => 1,
                    'msg' => 'Redis连接正常，读写测试通过'
                ]);
            } else {
                return json([
                    'code' => 0,
                    'msg' => 'Redis连接异常，读写测试失败'
                ]);
            }

        } catch (\Exception $e) {
            return json([
                'code' => 0,
                'msg' => 'Redis连接失败: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * 获取缓存统计信息
     */
    private function getCacheStats() {
        if(!$this->checkPermission(14)) {
            return json(['code' => 0, 'msg' => '您没有查看权限']);
        }

        try {
            // 获取Redis连接
            $redis = Cache::store('redis')->handler();

            // 获取缓存统计
            $activityRewardsKeys = $redis->keys('activity_rewards_*');
            $userDrawKeys = $redis->keys('user_draw_*');
            $deviceSigninKeys = $redis->keys('device_signin_*');
            $signinUnavailableKeys = $redis->keys('signin_unavailable_*');

            // 获取内存信息
            $memoryInfo = $redis->info('memory');
            $totalMemory = 'N/A';
            if (is_array($memoryInfo) && isset($memoryInfo['used_memory_human'])) {
                $totalMemory = $memoryInfo['used_memory_human'];
            }

            $stats = [
                'activity_rewards' => is_array($activityRewardsKeys) ? count($activityRewardsKeys) : 0,
                'user_draw' => is_array($userDrawKeys) ? count($userDrawKeys) : 0,
                'device_signin' => is_array($deviceSigninKeys) ? count($deviceSigninKeys) : 0,
                'signin_unavailable' => is_array($signinUnavailableKeys) ? count($signinUnavailableKeys) : 0,
                'total_memory' => $totalMemory
            ];

            $redis->close();

            return json(['code' => 1, 'msg' => '获取成功', 'data' => $stats]);

        } catch (\Exception $e) {
            return json(['code' => 0, 'msg' => '获取缓存统计失败: ' . $e->getMessage()]);
        }
    }


}
