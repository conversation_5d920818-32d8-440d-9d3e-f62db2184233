<?php

namespace app\common\controller;

use think\App;
use think\Request;

/**
 * 管理员基础控制器
 * 统一管理员权限检查逻辑
 */
class AdminBaseController extends BaseController
{
    /**
     * 当前管理员ID
     * @var int|null
     */
    protected $admin = null;

    /**
     * 构造方法 - 统一管理员权限检查
     * @param App $app
     * @param Request $request
     */
    public function __construct(App $app, Request $request)
    {
        parent::__construct($app, $request);
        
        // 统一的管理员权限检查
        $this->checkAdminPermission();
    }

    /**
     * 检查管理员权限
     * @throws \think\exception\HttpResponseException
     */
    protected function checkAdminPermission()
    {
        // 启动session（如果还没有启动）
        if (session_status() == PHP_SESSION_NONE) {
            session_start();
        }

        $admin = $this->admin = $_SESSION['admin'] ?? null;
        
        if ($admin == 0 || $admin == null) {
            // 获取当前请求的完整URL作为回调地址
            $callback = $this->request->url(true);
            $loginUrl = "/manage/login?type=admin&callback=" . urlencode($callback);
            
            // 如果是AJAX请求，返回JSON响应
            if ($this->request->isAjax()) {
                $response = json([
                    'code' => 401,
                    'msg' => '管理员权限验证失败，请重新登录',
                    'redirect' => $loginUrl
                ]);
                throw new \think\exception\HttpResponseException($response);
            }
            
            // 普通请求直接重定向
            header("Location: " . $loginUrl);
            exit;
        }
    }

    /**
     * 获取当前管理员ID
     * @return int|null
     */
    protected function getAdminId()
    {
        return $this->admin;
    }

    /**
     * 检查管理员是否有特定权限（预留接口，可根据需要扩展）
     * @param string $permission 权限标识
     * @return bool
     */
    protected function hasPermission($permission = '')
    {
        // 目前所有管理员都有相同权限，后续可以根据需要扩展权限系统
        return !empty($this->admin);
    }

    /**
     * 记录管理员操作日志（预留接口）
     * @param string $action 操作类型
     * @param string $description 操作描述
     * @param array $data 相关数据
     */
    protected function logAdminAction($action, $description, $data = [])
    {
        // 预留接口，可以根据需要实现管理员操作日志记录
        trace("Admin Action - ID: {$this->admin}, Action: {$action}, Description: {$description}", 'info');
    }

    /**
     * 统一的成功响应格式
     * @param mixed $data 响应数据
     * @param string $msg 响应消息
     * @return \think\Response
     */
    protected function success($data = [], $msg = '操作成功')
    {
        return json([
            'code' => 1,
            'msg' => $msg,
            'data' => $data
        ]);
    }

    /**
     * 统一的错误响应格式
     * @param string $msg 错误消息
     * @param int $code 错误代码
     * @param mixed $data 额外数据
     * @return \think\Response
     */
    protected function error($msg = '操作失败', $code = 0, $data = [])
    {
        return json([
            'code' => $code,
            'msg' => $msg,
            'data' => $data
        ]);
    }

    /**
     * 验证请求参数
     * @param array $rules 验证规则
     * @param array $data 要验证的数据，默认为当前请求参数
     * @return array|false 验证通过返回数据，失败返回false
     */
    protected function validateParams($rules, $data = null)
    {
        if ($data === null) {
            $data = $this->request->param();
        }

        $validate = \think\facade\Validate::rule($rules);
        
        if (!$validate->check($data)) {
            // 可以通过 $this->getValidateError() 获取错误信息
            $this->validateError = $validate->getError();
            return false;
        }

        return $data;
    }

    /**
     * 获取验证错误信息
     * @var string
     */
    protected $validateError = '';

    /**
     * 获取最后的验证错误信息
     * @return string
     */
    protected function getValidateError()
    {
        return $this->validateError;
    }
}
