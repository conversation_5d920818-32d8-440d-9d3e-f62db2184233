<?php

namespace app\manage\controller\manage;

use app\common\controller\AdminBaseController;
use app\manage\model\UpdateConfig as UpdateConfigModel;
use app\manage\model\UpdateGroup as UpdateGroupModel;
use think\facade\View;
use think\facade\Cache;
use app\common\service\RedisService;

/**
 * 更新配置管理控制器
 * 需要管理员权限
 */
class UpdateConfig extends AdminBaseController
{
    /**
     * 更新配置列表页面
     */
    public function index()
    {
        if($this->request->isPost()) {
            // 处理POST请求
            $action = $this->request->param('action', '');

            switch($action) {
                case 'save':
                    return $this->saveConfig();
                case 'delete':
                    return $this->deleteConfig();
                case 'list':
                    return $this->getList();
                case 'detail':
                    return $this->getDetail();
                case 'toggle':
                    return $this->toggleConfig();
                case 'refreshCache':
                    return $this->refreshCache();
                case 'refreshGroupsCache':
                    return $this->refreshGroupsCache();
                case 'setForceUpdate':
                    return $this->setForceUpdate();
                case 'getForceUpdateStatus':
                    return $this->getForceUpdateStatus();
                default:
                    return json(['code' => 0, 'msg' => '未知操作']);
            }
        }

        $configs = UpdateConfigModel::getAll();

        // 为每个配置获取群组统计（简化处理，因为白名单群组表结构不同）
        foreach ($configs as &$config) {
            $config['group_count'] = 0; // 暂时设为0，后续可以根据实际需求调整
        }

        return View::fetch('update_config/index', [
            'configs' => $configs
        ]);
    }

    /**
     * 添加/编辑配置页面
     */
    public function edit()
    {
        $id = $this->request->param('id');
        
        if ($id) {
            // 编辑模式
            $config = UpdateConfigModel::find($id);
            if (!$config) {
                return json(['code' => 0, 'msg' => '配置不存在']);
            }

            // 获取群组信息（白名单群组表没有app_name字段，这里简化处理）
            $groups = UpdateGroupModel::getActiveGroups();
            $config['group_data'] = [];

            return View::fetch('update_config/edit', [
                'config' => $config,
                'isEdit' => true,
                'groups' => $groups
            ]);
        } else {
            // 新增模式
            return View::fetch('update_config/edit', [
                'config' => null,
                'isEdit' => false,
                'groups' => []
            ]);
        }
    }

    /**
     * 保存配置（私有方法）
     */
    private function saveConfig()
    {
        $data = $this->request->param();

        try {
            // 保存配置
            $config = UpdateConfigModel::createOrUpdate($data);

            // 处理群组数据
            if (isset($data['groups']) && is_array($data['groups'])) {
                $this->saveGroups($data['name'], $data['groups']);
            }

            return json(['code' => 1, 'msg' => '保存成功', 'url' => '/manage/admin/update-config']);
        } catch (\Exception $e) {
            return json(['code' => 0, 'msg' => '保存失败: ' . $e->getMessage()]);
        }
    }

    /**
     * 删除配置（私有方法）
     */
    private function deleteConfig()
    {
        $id = $this->request->param('id');
        
        if (!$id) {
            return json(['code' => 0, 'msg' => '参数错误']);
        }

        try {
            $config = UpdateConfigModel::find($id);
            if (!$config) {
                return json(['code' => 0, 'msg' => '配置不存在']);
            }

            $appName = $config['name'];

            // 删除配置
            if (method_exists(UpdateConfigModel::class, 'deleteConfig')) {
                UpdateConfigModel::deleteConfig($id);
            } else {
                $config->delete();
            }

            // 删除相关群组（暂时跳过，因为方法不存在）
            // UpdateGroupModel::deleteByApp($appName);

            return json(['code' => 1, 'msg' => '删除成功']);
        } catch (\Exception $e) {
            return json(['code' => 0, 'msg' => '删除失败: ' . $e->getMessage()]);
        }
    }

    /**
     * 群组管理页面
     */
    public function groups()
    {
        $appName = $this->request->param('name');

        if (!$appName) {
            return json(['code' => 0, 'msg' => '参数错误']);
        }

        $config = UpdateConfigModel::where('name', $appName)->find();
        if (!$config) {
            return json(['code' => 0, 'msg' => '应用配置不存在']);
        }

        $groups = UpdateGroupModel::getActiveGroups();

        // 简化群组数据处理
        $groupData = [];
        foreach ($groups as $group) {
            $groupData[] = $group;
        }

        return View::fetch('update_config/groups', [
            'config' => $config,
            'groups' => $groupData,
            'appName' => $appName
        ]);
    }

    /**
     * 获取更新配置列表（API方法）
     */
    private function getList()
    {
        try {
            $configs = UpdateConfigModel::getAll();

            // 为每个配置获取群组信息
            foreach ($configs as &$config) {
                $groups = UpdateGroupModel::getByAppName($config['name']);
                $config['groups'] = $groups;
                $config['group_count'] = count($groups);
            }

            return json(['code' => 1, 'msg' => '获取成功', 'data' => $configs]);
        } catch (\Exception $e) {
            return json(['code' => 0, 'msg' => '获取失败: ' . $e->getMessage()]);
        }
    }

    /**
     * 获取单个更新配置（API方法）
     */
    private function getDetail()
    {
        $id = $this->request->param('id');

        if (!$id) {
            return json(['code' => 0, 'msg' => '参数错误']);
        }

        try {
            $config = UpdateConfigModel::find($id);
            if (!$config) {
                return json(['code' => 0, 'msg' => '配置不存在']);
            }

            // 获取群组信息
            $groups = UpdateGroupModel::getByAppName($config['name']);
            $config['groups'] = $groups;

            return json(['code' => 1, 'msg' => '获取成功', 'data' => $config]);
        } catch (\Exception $e) {
            return json(['code' => 0, 'msg' => '获取失败: ' . $e->getMessage()]);
        }
    }

    /**
     * 启用/禁用配置（API方法）
     */
    private function toggleConfig()
    {
        $id = $this->request->param('id');

        if (!$id) {
            return json(['code' => 0, 'msg' => '参数错误']);
        }

        try {
            $config = UpdateConfigModel::find($id);
            if (!$config) {
                return json(['code' => 0, 'msg' => '配置不存在']);
            }

            $result = UpdateConfigModel::toggleActive($id);

            if ($result) {
                // 清除缓存
                $this->clearCache($config['name']);

                // 记录管理员操作
                $this->logAdminAction('update_config_toggle', "切换更新配置状态: {$config['name']}", ['id' => $id, 'name' => $config['name']]);

                return json(['code' => 1, 'msg' => '操作成功']);
            } else {
                return json(['code' => 0, 'msg' => '操作失败']);
            }
        } catch (\Exception $e) {
            return json(['code' => 0, 'msg' => '操作失败: ' . $e->getMessage()]);
        }
    }

    /**
     * 刷新缓存（API方法）
     */
    private function refreshCache()
    {
        $appName = $this->request->param('name');

        try {
            if ($appName) {
                // 刷新指定应用的缓存
                $this->clearCache($appName);
                $msg = "应用 {$appName} 缓存刷新成功";
                $this->logAdminAction('refresh_cache', "刷新应用缓存: {$appName}");
            } else {
                // 刷新所有应用的缓存
                $apps = UpdateConfigModel::getAppList();
                foreach ($apps as $app) {
                    $this->clearCache($app);
                }
                $msg = "所有应用缓存刷新成功";
                $this->logAdminAction('refresh_cache', "刷新所有应用缓存");
            }

            return json(['code' => 1, 'msg' => $msg]);
        } catch (\Exception $e) {
            return json(['code' => 0, 'msg' => '缓存刷新失败: ' . $e->getMessage()]);
        }
    }

    /**
     * 刷新Groups缓存（API方法）
     */
    private function refreshGroupsCache()
    {
        try {
            $groupsCacheKey = "active_groups";

            // 删除Groups缓存
            RedisService::delete($groupsCacheKey);
            Cache::delete($groupsCacheKey);

            return json(['code' => 1, 'msg' => 'Groups缓存刷新成功']);
        } catch (\Exception $e) {
            return json(['code' => 0, 'msg' => 'Groups缓存刷新失败: ' . $e->getMessage()]);
        }
    }

    /**
     * 设置强制更新标志（API方法）
     */
    private function setForceUpdate()
    {
        try {
            $appName = $this->request->post('app_name', 'bns-helper');
            $force = $this->request->post('force', false);

            $cacheKey = "force_update:{$appName}";

            if ($force) {
                // 设置强制更新标志，缓存1小时
                RedisService::setex($cacheKey, 3600, true);
                $msg = "强制更新标志已设置，所有在线客户端将在下次心跳时收到更新通知";
            } else {
                // 清除强制更新标志
                RedisService::delete($cacheKey);
                $msg = "强制更新标志已清除";
            }

            return json(['code' => 1, 'msg' => $msg]);
        } catch (\Exception $e) {
            return json(['code' => 0, 'msg' => '设置强制更新标志失败: ' . $e->getMessage()]);
        }
    }

    /**
     * 获取强制更新状态（API方法）
     */
    private function getForceUpdateStatus()
    {
        try {
            $appName = $this->request->get('app_name', 'bns-helper');
            $beta = $this->request->get('beta', 0); // 0=正式版，1=测试版

            // 首先尝试从数据库获取
            $config = UpdateConfigModel::getByAppName($appName, $beta);

            if ($config) {
                return json([
                    'code' => 1,
                    'data' => [
                        'app_name' => $config->name,
                        'beta' => $config->beta,
                        'force_update' => $config->force_update,
                        'version' => $config->version,
                        'source' => 'database'
                    ]
                ]);
            }

            // 如果数据库没有，尝试从Redis缓存获取（兼容旧版本）
            $cacheKey = "force_update:{$appName}";
            $forceUpdate = RedisService::get($cacheKey);

            return json([
                'code' => 1,
                'data' => [
                    'app_name' => $appName,
                    'beta' => $beta,
                    'force_update' => !empty($forceUpdate),
                    'source' => 'cache',
                    'cache_key' => $cacheKey
                ]
            ]);
        } catch (\Exception $e) {
            return json(['code' => 0, 'msg' => '获取强制更新状态失败: ' . $e->getMessage()]);
        }
    }

    /**
     * 保存群组数据
     */
    private function saveGroups($appName, $groups)
    {
        // 删除现有群组
        UpdateGroupModel::deleteByApp($appName);

        // 保存新群组
        foreach ($groups as $groupType => $groupIds) {
            if (is_array($groupIds) && !empty($groupIds)) {
                UpdateGroupModel::batchCreate($appName, $groupType, $groupIds);
            }
        }
    }

    /**
     * 清除缓存
     */
    private function clearCache($appName)
    {
        $cacheKey = "update_config:{$appName}";
        $groupsCacheKey = "active_groups";

        // 删除Redis缓存（Go服务端使用的缓存）
        try {
            RedisService::delete($cacheKey);
            // 同时清除Groups缓存
            RedisService::delete($groupsCacheKey);
        } catch (\Exception $e) {
            // 记录错误但不影响主流程
            trace('Failed to clear Redis cache: ' . $e->getMessage(), 'error');
        }

        // 删除PHP本地缓存
        Cache::delete($cacheKey);
        Cache::delete($groupsCacheKey);
    }

    /**
     * 记录管理员操作日志
     */
    private function logAdminAction($action, $description, $data = []) {
        try {
            \think\facade\Db::name('admin_logs')->insert([
                'admin_id' => $this->getAdminId(),
                'action' => $action,
                'description' => $description,
                'data' => json_encode($data, JSON_UNESCAPED_UNICODE),
                'ip' => $this->request->ip(),
                'user_agent' => $this->request->header('User-Agent'),
                'created_at' => date('Y-m-d H:i:s')
            ]);
        } catch (\Exception $e) {
            // 记录日志失败不影响主流程
            trace('Failed to log admin action: ' . $e->getMessage(), 'error');
        }
    }
}
